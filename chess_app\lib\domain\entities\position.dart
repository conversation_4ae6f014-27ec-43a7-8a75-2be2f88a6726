import 'package:equatable/equatable.dart';

/// Represents a position on the chess board
class Position extends Equatable {
  final int row;
  final int col;

  const Position({required this.row, required this.col});

  /// Creates a position from algebraic notation (e.g., "e4")
  factory Position.fromAlgebraic(String algebraic) {
    if (algebraic.length != 2) {
      throw ArgumentError('Invalid algebraic notation: $algebraic');
    }
    
    final col = algebraic.codeUnitAt(0) - 'a'.codeUnitAt(0);
    final row = 8 - int.parse(algebraic[1]);
    
    return Position(row: row, col: col);
  }

  /// Converts position to algebraic notation (e.g., "e4")
  String toAlgebraic() {
    final colChar = String.fromCharCode('a'.codeUnitAt(0) + col);
    final rowChar = (8 - row).toString();
    return '$colChar$rowChar';
  }

  /// Checks if the position is within the board bounds
  bool get isValid => row >= 0 && row < 8 && col >= 0 && col < 8;

  /// Creates a new position with offset
  Position offset(int rowOffset, int colOffset) {
    return Position(row: row + rowOffset, col: col + colOffset);
  }

  @override
  List<Object> get props => [row, col];

  @override
  String toString() => 'Position(${toAlgebraic()})';
}
