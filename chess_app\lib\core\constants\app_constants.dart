/// Application constants
class AppConstants {
  // Board dimensions
  static const int boardSize = 8;
  static const double squareSize = 48.0;
  
  // Colors
  static const lightSquareColor = 0xFFF0D9B5;
  static const darkSquareColor = 0xFFB58863;
  static const selectedSquareColor = 0xFFFFFF00;
  static const validMoveColor = 0xFF00FF00;
  static const lastMoveColor = 0xFF0000FF;
  
  // Animation durations
  static const moveDuration = Duration(milliseconds: 300);
  static const highlightDuration = Duration(milliseconds: 150);
  
  // Asset paths
  static const String chessPiecesPath = 'assets/chess_pieces/';
  static const String soundsPath = 'assets/sounds/';
  static const String imagesPath = 'assets/images/';
  
  // Game settings
  static const double pieceSize = 40.0;
  static const double boardPadding = 16.0;
  
  // UI constants
  static const double appBarHeight = 56.0;
  static const double bottomBarHeight = 80.0;
}

/// Chess piece asset helper
class PieceAssets {
  static const String _basePath = AppConstants.chessPiecesPath;
  
  static String getPieceAsset(String color, String type) {
    return '$_basePath${color}_$type.png';
  }
  
  static const Map<String, String> pieceAssets = {
    'white_king': '${_basePath}white_king.png',
    'white_queen': '${_basePath}white_queen.png',
    'white_rook': '${_basePath}white_rook.png',
    'white_bishop': '${_basePath}white_bishop.png',
    'white_knight': '${_basePath}white_knight.png',
    'white_pawn': '${_basePath}white_pawn.png',
    'black_king': '${_basePath}black_king.png',
    'black_queen': '${_basePath}black_queen.png',
    'black_rook': '${_basePath}black_rook.png',
    'black_bishop': '${_basePath}black_bishop.png',
    'black_knight': '${_basePath}black_knight.png',
    'black_pawn': '${_basePath}black_pawn.png',
  };
}
