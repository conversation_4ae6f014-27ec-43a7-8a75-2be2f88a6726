import 'package:equatable/equatable.dart';
import '../../domain/entities/game_state.dart';
import '../../domain/entities/position.dart';
import '../../domain/entities/move.dart';
import '../../domain/entities/piece.dart';

/// Base class for all game BLoC states
abstract class GameBlocState extends Equatable {
  const GameBlocState();

  @override
  List<Object?> get props => [];
}

/// Initial state when the game is not started
class GameInitial extends GameBlocState {
  const GameInitial();
}

/// State when the game is loading
class GameLoading extends GameBlocState {
  final String? message;
  
  const GameLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// State when the game is in progress
class GameInProgress extends GameBlocState {
  final GameState gameState;
  final Position? selectedSquare;
  final List<Position> validMoves;
  final List<Position> highlightedSquares;
  final bool isPlayerTurn;
  final PieceColor playerColor;
  final bool isAIGame;
  final bool hintsEnabled;
  final bool soundEnabled;
  final bool animationsEnabled;
  final Duration? whiteTime;
  final Duration? blackTime;
  final bool isPaused;
  final List<Move> capturedPieces;
  final int? currentMoveIndex; // For move navigation

  const GameInProgress({
    required this.gameState,
    this.selectedSquare,
    this.validMoves = const [],
    this.highlightedSquares = const [],
    required this.isPlayerTurn,
    required this.playerColor,
    this.isAIGame = false,
    this.hintsEnabled = false,
    this.soundEnabled = true,
    this.animationsEnabled = true,
    this.whiteTime,
    this.blackTime,
    this.isPaused = false,
    this.capturedPieces = const [],
    this.currentMoveIndex,
  });

  /// Creates a copy of the state with updated properties
  GameInProgress copyWith({
    GameState? gameState,
    Position? selectedSquare,
    List<Position>? validMoves,
    List<Position>? highlightedSquares,
    bool? isPlayerTurn,
    PieceColor? playerColor,
    bool? isAIGame,
    bool? hintsEnabled,
    bool? soundEnabled,
    bool? animationsEnabled,
    Duration? whiteTime,
    Duration? blackTime,
    bool? isPaused,
    List<Move>? capturedPieces,
    int? currentMoveIndex,
    bool clearSelectedSquare = false,
  }) {
    return GameInProgress(
      gameState: gameState ?? this.gameState,
      selectedSquare: clearSelectedSquare ? null : (selectedSquare ?? this.selectedSquare),
      validMoves: validMoves ?? this.validMoves,
      highlightedSquares: highlightedSquares ?? this.highlightedSquares,
      isPlayerTurn: isPlayerTurn ?? this.isPlayerTurn,
      playerColor: playerColor ?? this.playerColor,
      isAIGame: isAIGame ?? this.isAIGame,
      hintsEnabled: hintsEnabled ?? this.hintsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      animationsEnabled: animationsEnabled ?? this.animationsEnabled,
      whiteTime: whiteTime ?? this.whiteTime,
      blackTime: blackTime ?? this.blackTime,
      isPaused: isPaused ?? this.isPaused,
      capturedPieces: capturedPieces ?? this.capturedPieces,
      currentMoveIndex: currentMoveIndex ?? this.currentMoveIndex,
    );
  }

  @override
  List<Object?> get props => [
        gameState,
        selectedSquare,
        validMoves,
        highlightedSquares,
        isPlayerTurn,
        playerColor,
        isAIGame,
        hintsEnabled,
        soundEnabled,
        animationsEnabled,
        whiteTime,
        blackTime,
        isPaused,
        capturedPieces,
        currentMoveIndex,
      ];
}

/// State when the game has finished
class GameFinished extends GameBlocState {
  final GameState gameState;
  final GameResult result;
  final String reason;
  final PieceColor playerColor;
  final bool isAIGame;

  const GameFinished({
    required this.gameState,
    required this.result,
    required this.reason,
    required this.playerColor,
    this.isAIGame = false,
  });

  @override
  List<Object?> get props => [gameState, result, reason, playerColor, isAIGame];
}

/// State when there's an error
class GameError extends GameBlocState {
  final String message;
  final GameState? gameState;

  const GameError({
    required this.message,
    this.gameState,
  });

  @override
  List<Object?> get props => [message, gameState];
}

/// State when AI is thinking
class AIThinking extends GameBlocState {
  final GameState gameState;
  final PieceColor playerColor;
  final Duration? thinkingTime;

  const AIThinking({
    required this.gameState,
    required this.playerColor,
    this.thinkingTime,
  });

  @override
  List<Object?> get props => [gameState, playerColor, thinkingTime];
}

/// State when a draw is offered
class DrawOffered extends GameBlocState {
  final GameState gameState;
  final PieceColor offeringPlayer;
  final PieceColor playerColor;

  const DrawOffered({
    required this.gameState,
    required this.offeringPlayer,
    required this.playerColor,
  });

  @override
  List<Object?> get props => [gameState, offeringPlayer, playerColor];
}

/// State when game is paused
class GamePaused extends GameBlocState {
  final GameState gameState;
  final PieceColor playerColor;
  final Duration? whiteTime;
  final Duration? blackTime;

  const GamePaused({
    required this.gameState,
    required this.playerColor,
    this.whiteTime,
    this.blackTime,
  });

  @override
  List<Object?> get props => [gameState, playerColor, whiteTime, blackTime];
}

/// State when game is saved successfully
class GameSaved extends GameBlocState {
  final String message;
  final GameState gameState;

  const GameSaved({
    required this.message,
    required this.gameState,
  });

  @override
  List<Object?> get props => [message, gameState];
}

/// State when game is exported successfully
class GameExported extends GameBlocState {
  final String pgnString;
  final GameState gameState;

  const GameExported({
    required this.pgnString,
    required this.gameState,
  });

  @override
  List<Object?> get props => [pgnString, gameState];
}
