import 'package:equatable/equatable.dart';
import 'position.dart';
import 'piece.dart';

/// Enum for special move types
enum MoveType {
  normal,
  capture,
  castling,
  enPassant,
  promotion,
}

/// Represents a chess move
class Move extends Equatable {
  final Position from;
  final Position to;
  final <PERSON><PERSON>ie<PERSON> piece;
  final ChessPiece? capturedPiece;
  final MoveType type;
  final PieceType? promotionPiece;
  final bool isCheck;
  final bool isCheckmate;

  const Move({
    required this.from,
    required this.to,
    required this.piece,
    this.capturedPiece,
    this.type = MoveType.normal,
    this.promotionPiece,
    this.isCheck = false,
    this.isCheckmate = false,
  });

  /// Creates a copy of the move with updated properties
  Move copyWith({
    Position? from,
    Position? to,
    ChessPiece? piece,
    ChessPiece? capturedPiece,
    MoveType? type,
    PieceType? promotionPiece,
    bool? isCheck,
    bool? isCheckmate,
  }) {
    return Move(
      from: from ?? this.from,
      to: to ?? this.to,
      piece: piece ?? this.piece,
      capturedPiece: captured<PERSON>iece ?? this.capturedPiece,
      type: type ?? this.type,
      promotionPiece: promotionPiece ?? this.promotionPiece,
      isCheck: isCheck ?? this.isCheck,
      isCheckmate: isCheckmate ?? this.isCheckmate,
    );
  }

  /// Converts the move to algebraic notation
  String toAlgebraicNotation() {
    final buffer = StringBuffer();
    
    // Add piece symbol (except for pawns)
    if (piece.type != PieceType.pawn) {
      buffer.write(piece.symbol.toUpperCase());
    }
    
    // Add capture notation
    if (type == MoveType.capture || type == MoveType.enPassant) {
      if (piece.type == PieceType.pawn) {
        buffer.write(from.toAlgebraic()[0]);
      }
      buffer.write('x');
    }
    
    // Add destination square
    buffer.write(to.toAlgebraic());
    
    // Add promotion notation
    if (type == MoveType.promotion && promotionPiece != null) {
      buffer.write('=');
      switch (promotionPiece!) {
        case PieceType.queen:
          buffer.write('Q');
          break;
        case PieceType.rook:
          buffer.write('R');
          break;
        case PieceType.bishop:
          buffer.write('B');
          break;
        case PieceType.knight:
          buffer.write('N');
          break;
        default:
          break;
      }
    }
    
    // Add check/checkmate notation
    if (isCheckmate) {
      buffer.write('#');
    } else if (isCheck) {
      buffer.write('+');
    }
    
    return buffer.toString();
  }

  /// Checks if this is a castling move
  bool get isCastling => type == MoveType.castling;
  
  /// Checks if this is an en passant move
  bool get isEnPassant => type == MoveType.enPassant;
  
  /// Checks if this is a promotion move
  bool get isPromotion => type == MoveType.promotion;
  
  /// Checks if this is a capture move
  bool get isCapture => type == MoveType.capture || type == MoveType.enPassant;

  @override
  List<Object?> get props => [
        from,
        to,
        piece,
        capturedPiece,
        type,
        promotionPiece,
        isCheck,
        isCheckmate,
      ];

  @override
  String toString() {
    return 'Move(${from.toAlgebraic()}-${to.toAlgebraic()}, ${piece.type.name}, ${type.name})';
  }
}
