import 'package:flutter/material.dart';
import '../../domain/entities/game_state.dart';
import '../../domain/entities/piece.dart';

/// Widget that displays game information like current player, status, etc.
class GameInfoWidget extends StatelessWidget {
  final GameState gameState;
  final Duration? whiteTime;
  final Duration? blackTime;

  const GameInfoWidget({
    Key? key,
    required this.gameState,
    this.whiteTime,
    this.blackTime,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCurrentPlayerInfo(context),
          const SizedBox(height: 8),
          _buildGameStatus(context),
          if (whiteTime != null || blackTime != null) ...[
            const SizedBox(height: 8),
            _buildTimers(context),
          ],
          const SizedBox(height: 8),
          _buildMoveInfo(context),
        ],
      ),
    );
  }

  Widget _buildCurrentPlayerInfo(BuildContext context) {
    final isWhiteTurn = gameState.currentPlayer == PieceColor.white;
    
    return Row(
      children: [
        Icon(
          Icons.circle,
          color: isWhiteTurn ? Colors.white : Colors.black,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          '${isWhiteTurn ? 'White' : 'Black'} to move',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildGameStatus(BuildContext context) {
    String statusText;
    Color statusColor;
    
    switch (gameState.status) {
      case GameStatus.playing:
        statusText = 'Game in progress';
        statusColor = Colors.green;
        break;
      case GameStatus.check:
        statusText = 'Check!';
        statusColor = Colors.orange;
        break;
      case GameStatus.checkmate:
        final winner = gameState.currentPlayer == PieceColor.white ? 'Black' : 'White';
        statusText = 'Checkmate! $winner wins';
        statusColor = Colors.red;
        break;
      case GameStatus.stalemate:
        statusText = 'Stalemate - Draw';
        statusColor = Colors.blue;
        break;
      case GameStatus.draw:
        statusText = 'Draw';
        statusColor = Colors.blue;
        break;
    }
    
    return Row(
      children: [
        Icon(
          _getStatusIcon(),
          color: statusColor,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          statusText,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTimers(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (whiteTime != null) _buildTimer(context, 'White', whiteTime!, Colors.white),
        if (blackTime != null) _buildTimer(context, 'Black', blackTime!, Colors.black),
      ],
    );
  }

  Widget _buildTimer(BuildContext context, String player, Duration time, Color color) {
    final minutes = time.inMinutes;
    final seconds = time.inSeconds % 60;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '$player: ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoveInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Move: ${gameState.fullMoveNumber}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        Text(
          'Half-moves: ${gameState.halfMoveClock}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  IconData _getStatusIcon() {
    switch (gameState.status) {
      case GameStatus.playing:
        return Icons.play_circle_outline;
      case GameStatus.check:
        return Icons.warning;
      case GameStatus.checkmate:
        return Icons.flag;
      case GameStatus.stalemate:
      case GameStatus.draw:
        return Icons.handshake;
    }
  }
}
