import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/chess_engine.dart';
import '../../domain/entities/game_state.dart';
import '../../domain/entities/move.dart';
import '../../domain/entities/piece.dart';
import '../../domain/entities/position.dart';
import '../../domain/entities/ai_player.dart';
import '../../domain/entities/game_mode.dart';
import 'game_event.dart';
import 'game_state_bloc.dart';

/// BLoC that manages the chess game state and logic
class GameBloc extends Bloc<GameEvent, GameBlocState> {
  final ChessEngine _chessEngine;
  AIPlayer? _aiPlayer;
  Timer? _gameTimer;
  Duration _whiteTime = const Duration(minutes: 10);
  Duration _blackTime = const Duration(minutes: 10);

  GameBloc({ChessEngine? chessEngine})
      : _chessEngine = chessEngine ?? ChessEngine(),
        super(const GameInitial()) {
    // Register event handlers
    on<StartNewGameEvent>(_onStartNewGame);
    on<LoadGameEvent>(_onLoadGame);
    on<MakeMoveEvent>(_onMakeMove);
    on<SelectSquareEvent>(_onSelectSquare);
    on<DeselectSquareEvent>(_onDeselectSquare);
    on<UndoMoveEvent>(_onUndoMove);
    on<RedoMoveEvent>(_onRedoMove);
    on<AIMovePieceEvent>(_onAIMove);
    on<PauseGameEvent>(_onPauseGame);
    on<ResumeGameEvent>(_onResumeGame);
    on<ResignGameEvent>(_onResignGame);
    on<OfferDrawEvent>(_onOfferDraw);
    on<AcceptDrawEvent>(_onAcceptDraw);
    on<DeclineDrawEvent>(_onDeclineDraw);
    on<UpdateGameSettingsEvent>(_onUpdateGameSettings);
    on<NavigateToMoveEvent>(_onNavigateToMove);
    on<FlipBoardEvent>(_onFlipBoard);
    on<ToggleHintsEvent>(_onToggleHints);
    on<SaveGameEvent>(_onSaveGame);
    on<ExportGameEvent>(_onExportGame);
    on<ImportGameEvent>(_onImportGame);
  }

  @override
  Future<void> close() {
    _gameTimer?.cancel();
    return super.close();
  }

  /// Handles starting a new game
  void _onStartNewGame(StartNewGameEvent event, Emitter<GameBlocState> emit) {
    try {
      emit(const GameLoading(message: 'Starting new game...'));

      _chessEngine.startNewGame();
      final gameState = _chessEngine.gameState;

      // Reset timers
      _whiteTime = const Duration(minutes: 10);
      _blackTime = const Duration(minutes: 10);

      emit(GameInProgress(
        gameState: gameState,
        isPlayerTurn: gameState.currentPlayer == event.playerColor,
        playerColor: event.playerColor,
        isAIGame: event.isAIGame,
        whiteTime: _whiteTime,
        blackTime: _blackTime,
      ));

      // Initialize AI player if needed
      if (event.isAIGame) {
        _aiPlayer =
            AIPlayer(difficulty: AIDifficulty.medium); // Default difficulty
      } else {
        _aiPlayer = null;
      }

      // Start timer if needed
      _startGameTimer();

      // If AI plays white and it's white's turn, make AI move
      if (event.isAIGame && event.playerColor == PieceColor.black) {
        add(const AIMovePieceEvent());
      }
    } catch (e) {
      emit(GameError(message: 'Failed to start new game: $e'));
    }
  }

  /// Handles loading a saved game
  void _onLoadGame(LoadGameEvent event, Emitter<GameBlocState> emit) {
    try {
      emit(const GameLoading(message: 'Loading game...'));

      _chessEngine.loadGameState(event.gameState);

      emit(GameInProgress(
        gameState: event.gameState,
        isPlayerTurn: true, // Assume player can move
        playerColor: PieceColor.white, // Default to white
        whiteTime: _whiteTime,
        blackTime: _blackTime,
      ));
    } catch (e) {
      emit(GameError(message: 'Failed to load game: $e'));
    }
  }

  /// Handles making a move
  void _onMakeMove(MakeMoveEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    try {
      // Validate and make the move
      if (!_chessEngine.isValidMove(event.move)) {
        emit(GameError(
          message: 'Invalid move',
          gameState: currentState.gameState,
        ));
        return;
      }

      final newGameState = _chessEngine.makeMove(event.move);

      // Get highlighted squares (last move)
      final highlightedSquares = [event.move.from, event.move.to];

      // Check if game is finished
      if (newGameState.isGameOver) {
        emit(GameFinished(
          gameState: newGameState,
          result: newGameState.result,
          reason: _getGameEndReason(newGameState),
          playerColor: currentState.playerColor,
          isAIGame: currentState.isAIGame,
        ));
        _gameTimer?.cancel();
        return;
      }

      // Update game state
      final newState = currentState.copyWith(
        gameState: newGameState,
        highlightedSquares: highlightedSquares,
        isPlayerTurn: newGameState.currentPlayer == currentState.playerColor,
        clearSelectedSquare: true,
        validMoves: [],
      );

      emit(newState);

      // If it's an AI game and it's AI's turn, trigger AI move
      if (currentState.isAIGame && !newState.isPlayerTurn) {
        add(const AIMovePieceEvent());
      }
    } catch (e) {
      emit(GameError(
        message: 'Failed to make move: $e',
        gameState: currentState.gameState,
      ));
    }
  }

  /// Handles square selection
  void _onSelectSquare(SelectSquareEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress || !currentState.isPlayerTurn) return;

    final piece = currentState.gameState.board.getPiece(event.position);

    // If no piece or wrong color, deselect
    if (piece == null || piece.color != currentState.playerColor) {
      emit(currentState.copyWith(
        clearSelectedSquare: true,
        validMoves: [],
      ));
      return;
    }

    // Get valid moves for the selected piece
    final validMoves = _chessEngine.generateLegalMoves(event.position);
    final validPositions = validMoves.map((move) => move.to).toList();

    emit(currentState.copyWith(
      selectedSquare: event.position,
      validMoves: validPositions,
    ));
  }

  /// Handles square deselection
  void _onDeselectSquare(
      DeselectSquareEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    emit(currentState.copyWith(
      clearSelectedSquare: true,
      validMoves: [],
    ));
  }

  /// Handles undo move
  void _onUndoMove(UndoMoveEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    // TODO: Implement undo functionality
    // This would require storing previous game states
    emit(const GameError(message: 'Undo not implemented yet'));
  }

  /// Handles redo move
  void _onRedoMove(RedoMoveEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    // TODO: Implement redo functionality
    emit(const GameError(message: 'Redo not implemented yet'));
  }

  /// Handles AI move
  void _onAIMove(AIMovePieceEvent event, Emitter<GameBlocState> emit) async {
    final currentState = state;
    if (currentState is! GameInProgress ||
        currentState.isPlayerTurn ||
        _aiPlayer == null) {
      return;
    }

    emit(AIThinking(
      gameState: currentState.gameState,
      playerColor: currentState.playerColor,
    ));

    try {
      // Get the best move from AI
      final bestMove = await _aiPlayer!.getBestMove(_chessEngine);

      if (bestMove != null) {
        // Make the AI move
        add(MakeMoveEvent(bestMove));
      } else {
        // No valid moves available
        emit(GameError(
          message: 'AI has no valid moves',
          gameState: currentState.gameState,
        ));
      }
    } catch (e) {
      emit(GameError(
        message: 'AI move failed: $e',
        gameState: currentState.gameState,
      ));
    }
  }

  /// Handles game pause
  void _onPauseGame(PauseGameEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    _gameTimer?.cancel();

    emit(GamePaused(
      gameState: currentState.gameState,
      playerColor: currentState.playerColor,
      whiteTime: _whiteTime,
      blackTime: _blackTime,
    ));
  }

  /// Handles game resume
  void _onResumeGame(ResumeGameEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GamePaused) return;

    emit(GameInProgress(
      gameState: currentState.gameState,
      isPlayerTurn:
          currentState.gameState.currentPlayer == currentState.playerColor,
      playerColor: currentState.playerColor,
      whiteTime: currentState.whiteTime,
      blackTime: currentState.blackTime,
    ));

    _startGameTimer();
  }

  /// Handles game resignation
  void _onResignGame(ResignGameEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    _gameTimer?.cancel();

    final winner = event.resigningPlayer == PieceColor.white
        ? GameResult.blackWins
        : GameResult.whiteWins;

    emit(GameFinished(
      gameState: currentState.gameState,
      result: winner,
      reason: 'Resignation',
      playerColor: currentState.playerColor,
      isAIGame: currentState.isAIGame,
    ));
  }

  /// Handles draw offer
  void _onOfferDraw(OfferDrawEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    emit(DrawOffered(
      gameState: currentState.gameState,
      offeringPlayer: currentState.gameState.currentPlayer,
      playerColor: currentState.playerColor,
    ));
  }

  /// Handles draw acceptance
  void _onAcceptDraw(AcceptDrawEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! DrawOffered) return;

    _gameTimer?.cancel();

    emit(GameFinished(
      gameState: currentState.gameState,
      result: GameResult.draw,
      reason: 'Draw by agreement',
      playerColor: currentState.playerColor,
    ));
  }

  /// Handles draw decline
  void _onDeclineDraw(DeclineDrawEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! DrawOffered) return;

    emit(GameInProgress(
      gameState: currentState.gameState,
      isPlayerTurn:
          currentState.gameState.currentPlayer == currentState.playerColor,
      playerColor: currentState.playerColor,
    ));
  }

  /// Handles game settings update
  void _onUpdateGameSettings(
      UpdateGameSettingsEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    emit(currentState.copyWith(
      soundEnabled: event.soundEnabled,
      animationsEnabled: event.animationsEnabled,
    ));
  }

  /// Handles move navigation
  void _onNavigateToMove(
      NavigateToMoveEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    // TODO: Implement move navigation
    emit(const GameError(message: 'Move navigation not implemented yet'));
  }

  /// Handles board flip
  void _onFlipBoard(FlipBoardEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    final newPlayerColor = currentState.playerColor == PieceColor.white
        ? PieceColor.black
        : PieceColor.white;

    emit(currentState.copyWith(playerColor: newPlayerColor));
  }

  /// Handles hints toggle
  void _onToggleHints(ToggleHintsEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    emit(currentState.copyWith(hintsEnabled: !currentState.hintsEnabled));
  }

  /// Handles game save
  void _onSaveGame(SaveGameEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    // TODO: Implement game saving
    emit(GameSaved(
      message: 'Game saved successfully',
      gameState: currentState.gameState,
    ));
  }

  /// Handles game export
  void _onExportGame(ExportGameEvent event, Emitter<GameBlocState> emit) {
    final currentState = state;
    if (currentState is! GameInProgress) return;

    // TODO: Implement PGN export
    emit(GameExported(
      pgnString: 'PGN export not implemented yet',
      gameState: currentState.gameState,
    ));
  }

  /// Handles game import
  void _onImportGame(ImportGameEvent event, Emitter<GameBlocState> emit) {
    try {
      // TODO: Implement PGN import
      emit(const GameError(message: 'PGN import not implemented yet'));
    } catch (e) {
      emit(GameError(message: 'Failed to import game: $e'));
    }
  }

  /// Starts the game timer
  void _startGameTimer() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final currentState = state;
      if (currentState is GameInProgress && !currentState.isPaused) {
        if (currentState.gameState.currentPlayer == PieceColor.white) {
          _whiteTime = _whiteTime - const Duration(seconds: 1);
        } else {
          _blackTime = _blackTime - const Duration(seconds: 1);
        }

        // Check for time out
        if (_whiteTime.isNegative || _blackTime.isNegative) {
          timer.cancel();
          final winner = _whiteTime.isNegative
              ? GameResult.blackWins
              : GameResult.whiteWins;
          // Use add instead of emit for timer events
          add(ResignGameEvent(winner == GameResult.blackWins
              ? PieceColor.white
              : PieceColor.black));
        }
        // Note: We don't emit state changes from timer to avoid issues
        // The UI will be updated when moves are made
      }
    });
  }

  /// Gets the reason for game end
  String _getGameEndReason(GameState gameState) {
    switch (gameState.status) {
      case GameStatus.checkmate:
        return 'Checkmate';
      case GameStatus.stalemate:
        return 'Stalemate';
      case GameStatus.draw:
        if (gameState.halfMoveClock >= 100) {
          return '50-move rule';
        }
        return 'Draw';
      default:
        return 'Game ended';
    }
  }
}
