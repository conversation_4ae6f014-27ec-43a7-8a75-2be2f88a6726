import 'package:equatable/equatable.dart';
import '../../domain/entities/move.dart';
import '../../domain/entities/position.dart';
import '../../domain/entities/piece.dart';
import '../../domain/entities/game_state.dart';

/// Base class for all game events
abstract class GameEvent extends Equatable {
  const GameEvent();

  @override
  List<Object?> get props => [];
}

/// Event to start a new game
class StartNewGameEvent extends GameEvent {
  final PieceColor playerColor;
  final bool isAIGame;
  
  const StartNewGameEvent({
    this.playerColor = PieceColor.white,
    this.isAIGame = false,
  });

  @override
  List<Object?> get props => [playerColor, isAIGame];
}

/// Event to load a saved game state
class LoadGameEvent extends GameEvent {
  final GameState gameState;
  
  const LoadGameEvent(this.gameState);

  @override
  List<Object?> get props => [gameState];
}

/// Event to make a move
class MakeMoveEvent extends GameEvent {
  final Move move;
  
  const MakeMoveEvent(this.move);

  @override
  List<Object?> get props => [move];
}

/// Event to select a square on the board
class SelectSquareEvent extends GameEvent {
  final Position position;
  
  const SelectSquareEvent(this.position);

  @override
  List<Object?> get props => [position];
}

/// Event to deselect the current square
class DeselectSquareEvent extends GameEvent {
  const DeselectSquareEvent();
}

/// Event to undo the last move
class UndoMoveEvent extends GameEvent {
  const UndoMoveEvent();
}

/// Event to redo a move
class RedoMoveEvent extends GameEvent {
  const RedoMoveEvent();
}

/// Event to request AI to make a move
class AIMovePieceEvent extends GameEvent {
  const AIMovePieceEvent();
}

/// Event to pause the game
class PauseGameEvent extends GameEvent {
  const PauseGameEvent();
}

/// Event to resume the game
class ResumeGameEvent extends GameEvent {
  const ResumeGameEvent();
}

/// Event to resign the game
class ResignGameEvent extends GameEvent {
  final PieceColor resigningPlayer;
  
  const ResignGameEvent(this.resigningPlayer);

  @override
  List<Object?> get props => [resigningPlayer];
}

/// Event to offer a draw
class OfferDrawEvent extends GameEvent {
  const OfferDrawEvent();
}

/// Event to accept a draw offer
class AcceptDrawEvent extends GameEvent {
  const AcceptDrawEvent();
}

/// Event to decline a draw offer
class DeclineDrawEvent extends GameEvent {
  const DeclineDrawEvent();
}

/// Event to update game settings
class UpdateGameSettingsEvent extends GameEvent {
  final bool soundEnabled;
  final bool animationsEnabled;
  final Duration? timeControl;
  
  const UpdateGameSettingsEvent({
    required this.soundEnabled,
    required this.animationsEnabled,
    this.timeControl,
  });

  @override
  List<Object?> get props => [soundEnabled, animationsEnabled, timeControl];
}

/// Event to navigate to a specific move in history
class NavigateToMoveEvent extends GameEvent {
  final int moveIndex;
  
  const NavigateToMoveEvent(this.moveIndex);

  @override
  List<Object?> get props => [moveIndex];
}

/// Event to flip the board
class FlipBoardEvent extends GameEvent {
  const FlipBoardEvent();
}

/// Event to show/hide move hints
class ToggleHintsEvent extends GameEvent {
  const ToggleHintsEvent();
}

/// Event to save the current game
class SaveGameEvent extends GameEvent {
  final String? gameName;
  
  const SaveGameEvent({this.gameName});

  @override
  List<Object?> get props => [gameName];
}

/// Event to export game in PGN format
class ExportGameEvent extends GameEvent {
  const ExportGameEvent();
}

/// Event to import game from PGN
class ImportGameEvent extends GameEvent {
  final String pgnString;
  
  const ImportGameEvent(this.pgnString);

  @override
  List<Object?> get props => [pgnString];
}
