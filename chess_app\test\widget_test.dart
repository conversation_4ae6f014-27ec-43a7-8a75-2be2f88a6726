// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:chess_app/main.dart';

void main() {
  testWidgets('Chess app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ChessApp());

    // Verify that the splash screen shows
    expect(find.text('Chess Master'), findsOneWidget);

    // Wait for navigation to home screen
    await tester.pumpAndSettle(const Duration(seconds: 4));

    // Verify home screen elements
    expect(find.text('Welcome to Chess Master'), findsOneWidget);
    expect(find.text('vs Human'), findsOneWidget);
    expect(find.text('vs Computer'), findsOneWidget);
  });
}
