import 'package:flutter/material.dart';

/// Manages animations for the chess app
class AnimationManager {
  static final AnimationManager _instance = AnimationManager._internal();
  factory AnimationManager() => _instance;
  AnimationManager._internal();

  bool _animationsEnabled = true;

  /// Enable or disable animations
  void setAnimationsEnabled(bool enabled) {
    _animationsEnabled = enabled;
  }

  /// Check if animations are enabled
  bool get areAnimationsEnabled => _animationsEnabled;

  /// Get move animation duration
  Duration get moveDuration => _animationsEnabled 
      ? const Duration(milliseconds: 300)
      : Duration.zero;

  /// Get highlight animation duration
  Duration get highlightDuration => _animationsEnabled 
      ? const Duration(milliseconds: 150)
      : Duration.zero;

  /// Get fade animation duration
  Duration get fadeDuration => _animationsEnabled 
      ? const Duration(milliseconds: 200)
      : Duration.zero;

  /// Get scale animation duration
  Duration get scaleDuration => _animationsEnabled 
      ? const Duration(milliseconds: 250)
      : Duration.zero;

  /// Get slide animation duration
  Duration get slideDuration => _animationsEnabled 
      ? const Duration(milliseconds: 400)
      : Duration.zero;
}

/// Animated piece widget for smooth piece movements
class AnimatedPieceWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Offset? fromPosition;
  final Offset? toPosition;
  final VoidCallback? onAnimationComplete;

  const AnimatedPieceWidget({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.fromPosition,
    this.toPosition,
    this.onAnimationComplete,
  }) : super(key: key);

  @override
  State<AnimatedPieceWidget> createState() => _AnimatedPieceWidgetState();
}

class _AnimatedPieceWidgetState extends State<AnimatedPieceWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _positionAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    if (widget.fromPosition != null && widget.toPosition != null) {
      _positionAnimation = Tween<Offset>(
        begin: widget.fromPosition!,
        end: widget.toPosition!,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      _controller.forward().then((_) {
        if (widget.onAnimationComplete != null) {
          widget.onAnimationComplete!();
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.fromPosition == null || widget.toPosition == null) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _positionAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: _positionAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}

/// Animated highlight widget for square highlighting
class AnimatedHighlightWidget extends StatefulWidget {
  final Widget child;
  final bool isHighlighted;
  final Color highlightColor;
  final Duration duration;

  const AnimatedHighlightWidget({
    Key? key,
    required this.child,
    required this.isHighlighted,
    this.highlightColor = Colors.yellow,
    this.duration = const Duration(milliseconds: 150),
  }) : super(key: key);

  @override
  State<AnimatedHighlightWidget> createState() => _AnimatedHighlightWidgetState();
}

class _AnimatedHighlightWidgetState extends State<AnimatedHighlightWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 0.3,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isHighlighted) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedHighlightWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isHighlighted != oldWidget.isHighlighted) {
      if (widget.isHighlighted) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (AnimationManager().areAnimationsEnabled)
          AnimatedBuilder(
            animation: _opacityAnimation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  color: widget.highlightColor.withOpacity(_opacityAnimation.value),
                ),
              );
            },
          ),
      ],
    );
  }
}

/// Animated scale widget for button presses and interactions
class AnimatedScaleWidget extends StatefulWidget {
  final Widget child;
  final bool isPressed;
  final double scaleFactor;
  final Duration duration;

  const AnimatedScaleWidget({
    Key? key,
    required this.child,
    required this.isPressed,
    this.scaleFactor = 0.95,
    this.duration = const Duration(milliseconds: 100),
  }) : super(key: key);

  @override
  State<AnimatedScaleWidget> createState() => _AnimatedScaleWidgetState();
}

class _AnimatedScaleWidgetState extends State<AnimatedScaleWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleFactor,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isPressed) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedScaleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPressed != oldWidget.isPressed) {
      if (widget.isPressed) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!AnimationManager().areAnimationsEnabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}

/// Slide transition for page navigation
class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Offset beginOffset;
  final Offset endOffset;

  SlidePageRoute({
    required this.child,
    this.beginOffset = const Offset(1.0, 0.0),
    this.endOffset = Offset.zero,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            if (!AnimationManager().areAnimationsEnabled) {
              return child;
            }

            return SlideTransition(
              position: Tween<Offset>(
                begin: beginOffset,
                end: endOffset,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOut,
              )),
              child: child,
            );
          },
          transitionDuration: AnimationManager().slideDuration,
        );
}
