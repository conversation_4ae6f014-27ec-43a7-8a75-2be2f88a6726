import 'package:flutter/material.dart';
import '../../domain/entities/piece.dart';
import '../../domain/entities/position.dart';
import '../../core/constants/app_constants.dart';

/// Widget that represents a single square on the chess board
class ChessSquareWidget extends StatelessWidget {
  final Position position;
  final ChessPiece? piece;
  final bool isSelected;
  final bool isValidMove;
  final bool isHighlighted;
  final bool isDragTarget;
  final Widget? child;

  const ChessSquareWidget({
    Key? key,
    required this.position,
    this.piece,
    this.isSelected = false,
    this.isValidMove = false,
    this.isHighlighted = false,
    this.isDragTarget = false,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLightSquare = (position.row + position.col) % 2 == 0;
    
    return AnimatedContainer(
      duration: AppConstants.highlightDuration,
      decoration: BoxDecoration(
        color: _getSquareColor(isLightSquare),
        border: _getBorder(),
      ),
      child: Stack(
        children: [
          // Background overlays
          if (isHighlighted) _buildHighlightOverlay(),
          if (isDragTarget) _buildDragTargetOverlay(),
          
          // Valid move indicator
          if (isValidMove) _buildValidMoveIndicator(),
          
          // Piece or child widget
          if (child != null)
            Center(child: child!)
          else if (piece != null)
            Center(child: _buildPieceWidget()),
          
          // Coordinate labels (only on edge squares)
          if (position.col == 0) _buildRowLabel(),
          if (position.row == 7) _buildColumnLabel(),
        ],
      ),
    );
  }

  Color _getSquareColor(bool isLight) {
    if (isSelected) {
      return Color(AppConstants.selectedSquareColor).withOpacity(0.8);
    }
    
    if (isLight) {
      return Color(AppConstants.lightSquareColor);
    } else {
      return Color(AppConstants.darkSquareColor);
    }
  }

  Border? _getBorder() {
    if (isSelected) {
      return Border.all(color: Colors.yellow, width: 3);
    }
    return null;
  }

  Widget _buildPieceWidget() {
    if (piece == null) return const SizedBox.shrink();
    
    return Image.asset(
      piece!.assetPath,
      width: AppConstants.pieceSize,
      height: AppConstants.pieceSize,
      fit: BoxFit.contain,
    );
  }

  Widget _buildValidMoveIndicator() {
    return Center(
      child: Container(
        width: piece != null ? 35 : 20,
        height: piece != null ? 35 : 20,
        decoration: BoxDecoration(
          color: piece != null 
              ? Colors.red.withOpacity(0.7)
              : Colors.green.withOpacity(0.6),
          shape: BoxShape.circle,
          border: piece != null 
              ? Border.all(color: Colors.red, width: 2)
              : null,
        ),
      ),
    );
  }

  Widget _buildHighlightOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Color(AppConstants.lastMoveColor).withOpacity(0.3),
      ),
    );
  }

  Widget _buildDragTargetOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.3),
        border: Border.all(color: Colors.blue, width: 2),
      ),
    );
  }

  Widget _buildRowLabel() {
    return Positioned(
      left: 2,
      top: 2,
      child: Text(
        '${8 - position.row}',
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: (position.row + position.col) % 2 == 0 
              ? Color(AppConstants.darkSquareColor)
              : Color(AppConstants.lightSquareColor),
        ),
      ),
    );
  }

  Widget _buildColumnLabel() {
    return Positioned(
      right: 2,
      bottom: 2,
      child: Text(
        String.fromCharCode('a'.codeUnitAt(0) + position.col),
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: (position.row + position.col) % 2 == 0 
              ? Color(AppConstants.darkSquareColor)
              : Color(AppConstants.lightSquareColor),
        ),
      ),
    );
  }
}
