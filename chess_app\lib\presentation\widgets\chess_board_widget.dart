import 'package:flutter/material.dart';
import '../../domain/entities/board.dart';
import '../../domain/entities/move.dart';
import '../../domain/entities/piece.dart';
import '../../domain/entities/position.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/sound_manager.dart';
import '../../core/utils/animation_manager.dart';
import 'chess_square_widget.dart';

/// Widget that displays the chess board
class ChessBoardWidget extends StatefulWidget {
  final Board board;
  final Function(Move) onMovePiece;
  final List<Position> highlightedSquares;
  final Position? selectedSquare;
  final List<Position> validMoves;
  final bool isPlayerTurn;
  final PieceColor playerColor;

  const ChessBoardWidget({
    Key? key,
    required this.board,
    required this.onMovePiece,
    this.highlightedSquares = const [],
    this.selectedSquare,
    this.validMoves = const [],
    this.isPlayerTurn = true,
    this.playerColor = PieceColor.white,
  }) : super(key: key);

  @override
  State<ChessBoardWidget> createState() => _ChessBoardWidgetState();
}

class _ChessBoardWidgetState extends State<ChessBoardWidget> {
  Position? _selectedSquare;
  List<Position> _validMoves = [];
  Position? _draggedPiecePosition;

  @override
  void initState() {
    super.initState();
    _selectedSquare = widget.selectedSquare;
    _validMoves = widget.validMoves;
  }

  @override
  void didUpdateWidget(ChessBoardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedSquare != oldWidget.selectedSquare) {
      _selectedSquare = widget.selectedSquare;
    }
    if (widget.validMoves != oldWidget.validMoves) {
      _validMoves = widget.validMoves;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.brown[800]!, width: 2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: AppConstants.boardSize,
            ),
            itemCount: 64,
            itemBuilder: (context, index) {
              final row = widget.playerColor == PieceColor.white
                  ? index ~/ 8
                  : 7 - (index ~/ 8);
              final col = widget.playerColor == PieceColor.white
                  ? index % 8
                  : 7 - (index % 8);
              final position = Position(row: row, col: col);

              return _buildSquare(position);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSquare(Position position) {
    final piece = widget.board.getPiece(position);
    final isSelected = _selectedSquare == position;
    final isValidMove = _validMoves.contains(position);
    final isHighlighted = widget.highlightedSquares.contains(position);
    final isDragTarget =
        _draggedPiecePosition != null && _validMoves.contains(position);

    return GestureDetector(
      onTap: () => _onSquareTapped(position),
      child: DragTarget<Position>(
        onWillAccept: (data) => _canAcceptDrop(data, position),
        onAccept: (data) => _onPieceDropped(data, position),
        builder: (context, candidateData, rejectedData) {
          return ChessSquareWidget(
            position: position,
            piece: piece,
            isSelected: isSelected,
            isValidMove: isValidMove,
            isHighlighted: isHighlighted,
            isDragTarget: isDragTarget && candidateData.isNotEmpty,
            child: piece != null ? _buildDraggablePiece(piece, position) : null,
          );
        },
      ),
    );
  }

  Widget _buildDraggablePiece(ChessPiece piece, Position position) {
    if (!widget.isPlayerTurn || piece.color != widget.playerColor) {
      return _buildPieceImage(piece);
    }

    return Draggable<Position>(
      data: position,
      feedback: Material(
        color: Colors.transparent,
        child: Transform.scale(
          scale: 1.2,
          child: _buildPieceImage(piece),
        ),
      ),
      childWhenDragging: Container(),
      onDragStarted: () {
        setState(() {
          _draggedPiecePosition = position;
          _selectedSquare = position;
          // TODO: Get valid moves from chess engine
          _validMoves = [];
        });
      },
      onDragEnd: (details) {
        setState(() {
          _draggedPiecePosition = null;
        });
      },
      child: _buildPieceImage(piece),
    );
  }

  Widget _buildPieceImage(ChessPiece piece) {
    return Image.asset(
      piece.assetPath,
      width: AppConstants.pieceSize,
      height: AppConstants.pieceSize,
      fit: BoxFit.contain,
    );
  }

  void _onSquareTapped(Position position) {
    if (!widget.isPlayerTurn) return;

    final piece = widget.board.getPiece(position);

    if (_selectedSquare == null) {
      // No piece selected, select this piece if it belongs to the player
      if (piece != null && piece.color == widget.playerColor) {
        setState(() {
          _selectedSquare = position;
          // TODO: Get valid moves from chess engine
          _validMoves = [];
        });
      }
    } else {
      // A piece is already selected
      if (_selectedSquare == position) {
        // Clicked on the same square, deselect
        setState(() {
          _selectedSquare = null;
          _validMoves = [];
        });
      } else if (piece != null && piece.color == widget.playerColor) {
        // Clicked on another piece of the same color, select it
        setState(() {
          _selectedSquare = position;
          // TODO: Get valid moves from chess engine
          _validMoves = [];
        });
      } else if (_validMoves.contains(position)) {
        // Valid move, make the move
        _makeMove(_selectedSquare!, position);
      } else {
        // Invalid move, deselect
        setState(() {
          _selectedSquare = null;
          _validMoves = [];
        });
      }
    }
  }

  bool _canAcceptDrop(Position? from, Position to) {
    if (from == null || !widget.isPlayerTurn) return false;

    final piece = widget.board.getPiece(from);
    if (piece == null || piece.color != widget.playerColor) return false;

    return _validMoves.contains(to);
  }

  void _onPieceDropped(Position from, Position to) {
    _makeMove(from, to);
  }

  void _makeMove(Position from, Position to) {
    final piece = widget.board.getPiece(from);
    if (piece == null) return;

    final capturedPiece = widget.board.getPiece(to);
    final moveType = capturedPiece != null ? MoveType.capture : MoveType.normal;

    final move = Move(
      from: from,
      to: to,
      piece: piece,
      capturedPiece: capturedPiece,
      type: moveType,
    );

    // Play sound effect
    if (capturedPiece != null) {
      SoundManager().playCaptureSound();
    } else {
      SoundManager().playMoveSound();
    }

    widget.onMovePiece(move);

    setState(() {
      _selectedSquare = null;
      _validMoves = [];
      _draggedPiecePosition = null;
    });
  }
}
