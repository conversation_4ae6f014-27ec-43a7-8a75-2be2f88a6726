import 'dart:async';
import 'dart:math';
import 'chess_engine.dart';
import 'move.dart';
import 'piece.dart';
import 'position.dart';
import 'board.dart';
import 'game_state.dart';

/// Abstract interface for chess AI engines
abstract class ChessAIEngine {
  /// Gets the best move for the current position
  Future<Move?> getBestMove(ChessEngine engine, {Duration? timeLimit});

  /// Gets the engine name
  String get name;

  /// Gets the engine strength (0-3000 ELO range)
  int get strength;

  /// Sets the engine difficulty/strength
  void setStrength(int strength);

  /// Analyzes a position and returns evaluation
  Future<PositionAnalysis> analyzePosition(ChessEngine engine);

  /// Stops the current analysis
  void stop();

  /// Disposes the engine resources
  void dispose();
}

/// Position analysis result
class PositionAnalysis {
  final double evaluation; // In centipawns (100 = 1 pawn advantage)
  final Move? bestMove;
  final List<Move> principalVariation;
  final int depth;
  final Duration analysisTime;

  const PositionAnalysis({
    required this.evaluation,
    this.bestMove,
    this.principalVariation = const [],
    required this.depth,
    required this.analysisTime,
  });
}

/// Enhanced AI engine with advanced algorithms
class AdvancedChessAI extends ChessAIEngine {
  int _strength = 1500; // Default ELO
  bool _isAnalyzing = false;
  final Random _random = Random();

  // Piece values in centipawns
  static const Map<PieceType, int> _pieceValues = {
    PieceType.pawn: 100,
    PieceType.knight: 320,
    PieceType.bishop: 330,
    PieceType.rook: 500,
    PieceType.queen: 900,
    PieceType.king: 0,
  };

  // Position tables for piece placement evaluation
  static const List<List<int>> _pawnTable = [
    [0, 0, 0, 0, 0, 0, 0, 0],
    [50, 50, 50, 50, 50, 50, 50, 50],
    [10, 10, 20, 30, 30, 20, 10, 10],
    [5, 5, 10, 25, 25, 10, 5, 5],
    [0, 0, 0, 20, 20, 0, 0, 0],
    [5, -5, -10, 0, 0, -10, -5, 5],
    [5, 10, 10, -20, -20, 10, 10, 5],
    [0, 0, 0, 0, 0, 0, 0, 0]
  ];

  static const List<List<int>> _knightTable = [
    [-50, -40, -30, -30, -30, -30, -40, -50],
    [-40, -20, 0, 0, 0, 0, -20, -40],
    [-30, 0, 10, 15, 15, 10, 0, -30],
    [-30, 5, 15, 20, 20, 15, 5, -30],
    [-30, 0, 15, 20, 20, 15, 0, -30],
    [-30, 5, 10, 15, 15, 10, 5, -30],
    [-40, -20, 0, 5, 5, 0, -20, -40],
    [-50, -40, -30, -30, -30, -30, -40, -50],
  ];

  @override
  String get name => 'Advanced Chess AI';

  @override
  int get strength => _strength;

  @override
  void setStrength(int strength) {
    _strength = strength.clamp(800, 3000);
  }

  @override
  Future<Move?> getBestMove(ChessEngine engine, {Duration? timeLimit}) async {
    _isAnalyzing = true;
    final startTime = DateTime.now();
    timeLimit ??= Duration(milliseconds: _getThinkingTime());

    try {
      // Determine search depth based on strength
      final depth = _getSearchDepth();

      final analysis = await _searchBestMove(engine, depth, timeLimit);

      // Add some randomness for lower difficulties
      if (_strength < 1800 && _random.nextDouble() < 0.1) {
        final legalMoves = engine.generateAllLegalMoves();
        if (legalMoves.isNotEmpty) {
          return legalMoves[_random.nextInt(legalMoves.length)];
        }
      }

      return analysis.bestMove;
    } finally {
      _isAnalyzing = false;
    }
  }

  @override
  Future<PositionAnalysis> analyzePosition(ChessEngine engine) async {
    final startTime = DateTime.now();
    final depth = _getSearchDepth();

    return await _searchBestMove(engine, depth, const Duration(seconds: 5));
  }

  @override
  void stop() {
    _isAnalyzing = false;
  }

  @override
  void dispose() {
    stop();
  }

  /// Advanced minimax search with alpha-beta pruning and move ordering
  Future<PositionAnalysis> _searchBestMove(
      ChessEngine engine, int maxDepth, Duration timeLimit) async {
    final startTime = DateTime.now();
    Move? bestMove;
    double bestEval = double.negativeInfinity;
    final principalVariation = <Move>[];

    final legalMoves = engine.generateAllLegalMoves();
    if (legalMoves.isEmpty) {
      return PositionAnalysis(
        evaluation:
            engine.isInCheck(engine.gameState.currentPlayer) ? -99999 : 0,
        depth: 0,
        analysisTime: DateTime.now().difference(startTime),
      );
    }

    // Order moves for better alpha-beta pruning
    _orderMoves(legalMoves, engine);

    for (final move in legalMoves) {
      if (!_isAnalyzing || DateTime.now().difference(startTime) > timeLimit) {
        break;
      }

      // Make move
      final testEngine = ChessEngine();
      testEngine.loadGameState(engine.gameState);
      testEngine.makeMove(move);

      // Search deeper
      final eval = -await _minimax(
        testEngine,
        maxDepth - 1,
        double.negativeInfinity,
        double.infinity,
        false,
        startTime,
        timeLimit,
      );

      if (eval > bestEval) {
        bestEval = eval;
        bestMove = move;
      }
    }

    return PositionAnalysis(
      evaluation: bestEval,
      bestMove: bestMove,
      principalVariation: principalVariation,
      depth: maxDepth,
      analysisTime: DateTime.now().difference(startTime),
    );
  }

  /// Enhanced minimax with alpha-beta pruning
  Future<double> _minimax(
    ChessEngine engine,
    int depth,
    double alpha,
    double beta,
    bool isMaximizing,
    DateTime startTime,
    Duration timeLimit,
  ) async {
    if (!_isAnalyzing || DateTime.now().difference(startTime) > timeLimit) {
      return _evaluatePosition(engine);
    }

    if (depth == 0 || engine.gameState.isGameOver) {
      return _evaluatePosition(engine);
    }

    final legalMoves = engine.generateAllLegalMoves();
    if (legalMoves.isEmpty) {
      if (engine.isInCheck(engine.gameState.currentPlayer)) {
        return isMaximizing ? -99999.0 + depth : 99999.0 - depth; // Checkmate
      }
      return 0; // Stalemate
    }

    _orderMoves(legalMoves, engine);

    if (isMaximizing) {
      double maxEval = double.negativeInfinity;
      for (final move in legalMoves) {
        final testEngine = ChessEngine();
        testEngine.loadGameState(engine.gameState);
        testEngine.makeMove(move);

        final eval = await _minimax(
            testEngine, depth - 1, alpha, beta, false, startTime, timeLimit);
        maxEval = max(maxEval, eval);
        alpha = max(alpha, eval);

        if (beta <= alpha) break; // Alpha-beta cutoff
      }
      return maxEval;
    } else {
      double minEval = double.infinity;
      for (final move in legalMoves) {
        final testEngine = ChessEngine();
        testEngine.loadGameState(engine.gameState);
        testEngine.makeMove(move);

        final eval = await _minimax(
            testEngine, depth - 1, alpha, beta, true, startTime, timeLimit);
        minEval = min(minEval, eval);
        beta = min(beta, eval);

        if (beta <= alpha) break; // Alpha-beta cutoff
      }
      return minEval;
    }
  }

  /// Enhanced position evaluation
  double _evaluatePosition(ChessEngine engine) {
    if (engine.gameState.status == GameStatus.checkmate) {
      return engine.gameState.currentPlayer == PieceColor.white
          ? -99999
          : 99999;
    }

    if (engine.gameState.status == GameStatus.stalemate ||
        engine.gameState.status == GameStatus.draw) {
      return 0;
    }

    double score = 0;

    // Material evaluation
    score += _evaluateMaterial(engine.gameState.board);

    // Positional evaluation
    score += _evaluatePositions(engine.gameState.board);

    // King safety
    score += _evaluateKingSafety(engine.gameState.board);

    // Mobility
    score += _evaluateMobility(engine);

    // Pawn structure
    score += _evaluatePawnStructure(engine.gameState.board);

    return score;
  }

  double _evaluateMaterial(Board board) {
    double score = 0;

    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = board.getPieceAt(row, col);
        if (piece != null) {
          final value = _pieceValues[piece.type] ?? 0;
          score += piece.color == PieceColor.white ? value : -value;
        }
      }
    }

    return score;
  }

  double _evaluatePositions(Board board) {
    double score = 0;

    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = board.getPieceAt(row, col);
        if (piece != null) {
          final posValue = _getPositionValue(piece, row, col);
          score += piece.color == PieceColor.white ? posValue : -posValue;
        }
      }
    }

    return score;
  }

  int _getPositionValue(ChessPiece piece, int row, int col) {
    switch (piece.type) {
      case PieceType.pawn:
        return piece.color == PieceColor.white
            ? _pawnTable[row][col]
            : _pawnTable[7 - row][col];
      case PieceType.knight:
        return piece.color == PieceColor.white
            ? _knightTable[row][col]
            : _knightTable[7 - row][col];
      default:
        return 0;
    }
  }

  double _evaluateKingSafety(Board board) {
    // Simplified king safety evaluation
    return 0;
  }

  double _evaluateMobility(ChessEngine engine) {
    // Count legal moves for both sides
    final currentPlayer = engine.gameState.currentPlayer;
    final moves = engine.generateAllLegalMoves().length;

    // Switch sides temporarily to count opponent moves
    // This is a simplified approach
    return currentPlayer == PieceColor.white ? moves * 10 : -moves * 10;
  }

  double _evaluatePawnStructure(Board board) {
    // Simplified pawn structure evaluation
    return 0;
  }

  void _orderMoves(List<Move> moves, ChessEngine engine) {
    // Simple move ordering: captures first, then other moves
    moves.sort((a, b) {
      if (a.isCapture && !b.isCapture) return -1;
      if (!a.isCapture && b.isCapture) return 1;
      if (a.isCheck && !b.isCheck) return -1;
      if (!a.isCheck && b.isCheck) return 1;
      return 0;
    });
  }

  int _getSearchDepth() {
    if (_strength < 1000) return 2;
    if (_strength < 1400) return 3;
    if (_strength < 1800) return 4;
    if (_strength < 2200) return 5;
    return 6;
  }

  int _getThinkingTime() {
    if (_strength < 1000) return 500;
    if (_strength < 1400) return 1000;
    if (_strength < 1800) return 2000;
    if (_strength < 2200) return 3000;
    return 5000;
  }
}
