import 'package:flutter/material.dart';
import '../../domain/entities/piece.dart';
import '../../core/constants/app_constants.dart';

/// Widget that displays captured pieces
class CapturedPiecesWidget extends StatelessWidget {
  final List<ChessPiece> capturedPieces;
  final PieceColor color;
  final bool isTop;

  const CapturedPiecesWidget({
    Key? key,
    required this.capturedPieces,
    required this.color,
    this.isTop = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final pieces = capturedPieces.where((piece) => piece.color == color).toList();
    
    if (pieces.isEmpty) {
      return SizedBox(
        height: 40,
        child: Container(),
      );
    }

    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: Wrap(
              spacing: 2,
              runSpacing: 2,
              alignment: WrapAlignment.start,
              children: pieces.map((piece) => _buildCapturedPiece(piece)).toList(),
            ),
          ),
          if (pieces.isNotEmpty) _buildMaterialAdvantage(pieces),
        ],
      ),
    );
  }

  Widget _buildCapturedPiece(ChessPiece piece) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey[400]!, width: 0.5),
      ),
      child: Image.asset(
        piece.assetPath,
        width: 20,
        height: 20,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildMaterialAdvantage(List<ChessPiece> pieces) {
    final materialValue = _calculateMaterialValue(pieces);
    
    if (materialValue <= 0) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color == PieceColor.white ? Colors.white : Colors.black,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color == PieceColor.white ? Colors.black : Colors.white,
          width: 1,
        ),
      ),
      child: Text(
        '+$materialValue',
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: color == PieceColor.white ? Colors.black : Colors.white,
        ),
      ),
    );
  }

  int _calculateMaterialValue(List<ChessPiece> pieces) {
    int total = 0;
    for (final piece in pieces) {
      total += _getPieceValue(piece.type);
    }
    return total;
  }

  int _getPieceValue(PieceType type) {
    switch (type) {
      case PieceType.pawn:
        return 1;
      case PieceType.knight:
      case PieceType.bishop:
        return 3;
      case PieceType.rook:
        return 5;
      case PieceType.queen:
        return 9;
      case PieceType.king:
        return 0; // King is invaluable
    }
  }
}
