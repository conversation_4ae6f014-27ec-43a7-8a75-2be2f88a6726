import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/game_bloc.dart';
import '../bloc/game_event.dart';

/// Settings screen for app configuration
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _soundEnabled = true;
  bool _animationsEnabled = true;
  bool _hintsEnabled = false;
  bool _darkMode = false;
  double _aiDifficulty = 2.0; // 0=Easy, 1=Medium, 2=Hard, 3=Expert

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Game Settings
          _buildSectionHeader('Game Settings'),
          _buildSoundSetting(),
          _buildAnimationsSetting(),
          _buildHintsSetting(),
          
          const SizedBox(height: 24),
          
          // AI Settings
          _buildSectionHeader('AI Settings'),
          _buildAIDifficultySetting(),
          
          const SizedBox(height: 24),
          
          // Appearance Settings
          _buildSectionHeader('Appearance'),
          _buildDarkModeSetting(),
          
          const SizedBox(height: 24),
          
          // About Section
          _buildSectionHeader('About'),
          _buildAboutTile(),
          _buildVersionTile(),
          
          const SizedBox(height: 24),
          
          // Actions
          _buildSectionHeader('Actions'),
          _buildResetSettingsTile(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSoundSetting() {
    return Card(
      child: SwitchListTile(
        title: const Text('Sound Effects'),
        subtitle: const Text('Enable move sounds and notifications'),
        value: _soundEnabled,
        onChanged: (value) {
          setState(() {
            _soundEnabled = value;
          });
          _updateGameSettings();
        },
        secondary: const Icon(Icons.volume_up),
      ),
    );
  }

  Widget _buildAnimationsSetting() {
    return Card(
      child: SwitchListTile(
        title: const Text('Animations'),
        subtitle: const Text('Enable piece movement animations'),
        value: _animationsEnabled,
        onChanged: (value) {
          setState(() {
            _animationsEnabled = value;
          });
          _updateGameSettings();
        },
        secondary: const Icon(Icons.animation),
      ),
    );
  }

  Widget _buildHintsSetting() {
    return Card(
      child: SwitchListTile(
        title: const Text('Show Hints'),
        subtitle: const Text('Highlight possible moves'),
        value: _hintsEnabled,
        onChanged: (value) {
          setState(() {
            _hintsEnabled = value;
          });
          context.read<GameBloc>().add(const ToggleHintsEvent());
        },
        secondary: const Icon(Icons.lightbulb_outline),
      ),
    );
  }

  Widget _buildAIDifficultySetting() {
    final difficulties = ['Easy', 'Medium', 'Hard', 'Expert'];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.computer),
                const SizedBox(width: 16),
                Text(
                  'AI Difficulty',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Current: ${difficulties[_aiDifficulty.round()]}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Slider(
              value: _aiDifficulty,
              min: 0,
              max: 3,
              divisions: 3,
              label: difficulties[_aiDifficulty.round()],
              onChanged: (value) {
                setState(() {
                  _aiDifficulty = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDarkModeSetting() {
    return Card(
      child: SwitchListTile(
        title: const Text('Dark Mode'),
        subtitle: const Text('Use dark theme'),
        value: _darkMode,
        onChanged: (value) {
          setState(() {
            _darkMode = value;
          });
          // TODO: Implement theme switching
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Theme switching coming soon!')),
          );
        },
        secondary: const Icon(Icons.dark_mode),
      ),
    );
  }

  Widget _buildAboutTile() {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.info_outline),
        title: const Text('About Chess Master'),
        subtitle: const Text('Learn more about this app'),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () {
          showAboutDialog(
            context: context,
            applicationName: 'Chess Master',
            applicationVersion: '1.0.0',
            applicationIcon: const Icon(Icons.sports_esports, size: 48),
            children: [
              const Text('A complete chess application with AI opponents, puzzles, and analysis tools.'),
              const SizedBox(height: 16),
              const Text('Features:'),
              const Text('• Play against AI or human opponents'),
              const Text('• Multiple difficulty levels'),
              const Text('• Chess puzzles and tactics'),
              const Text('• Game analysis and history'),
              const Text('• Beautiful, intuitive interface'),
            ],
          );
        },
      ),
    );
  }

  Widget _buildVersionTile() {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.info),
        title: const Text('Version'),
        subtitle: const Text('1.0.0'),
        trailing: TextButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('You are using the latest version!')),
            );
          },
          child: const Text('Check for updates'),
        ),
      ),
    );
  }

  Widget _buildResetSettingsTile() {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.restore, color: Colors.red),
        title: const Text('Reset Settings'),
        subtitle: const Text('Restore default settings'),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Reset Settings'),
              content: const Text('Are you sure you want to reset all settings to default values?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _resetSettings();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  child: const Text('Reset'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _updateGameSettings() {
    context.read<GameBloc>().add(UpdateGameSettingsEvent(
      soundEnabled: _soundEnabled,
      animationsEnabled: _animationsEnabled,
    ));
  }

  void _resetSettings() {
    setState(() {
      _soundEnabled = true;
      _animationsEnabled = true;
      _hintsEnabled = false;
      _darkMode = false;
      _aiDifficulty = 2.0;
    });
    
    _updateGameSettings();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings reset to default values')),
    );
  }
}
