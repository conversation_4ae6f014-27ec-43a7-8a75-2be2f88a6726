import 'dart:math';
import 'chess_engine.dart';
import 'move.dart';
import 'piece.dart';
import 'position.dart';
import 'board.dart';
import 'game_state.dart';
import 'ai_engine_manager.dart';

/// Enum for AI difficulty levels
enum AIDifficulty {
  easy,
  medium,
  hard,
  expert,
}

/// AI player that can play chess moves
class AIPlayer {
  final AIDifficulty difficulty;
  final Random _random = Random();
  final AIEngineManager _engineManager = AIEngineManager();

  AIPlayer({this.difficulty = AIDifficulty.medium}) {
    _initializeEngine();
  }

  void _initializeEngine() async {
    await _engineManager.initialize();

    // Set engine type and strength based on difficulty
    switch (difficulty) {
      case AIDifficulty.easy:
        await _engineManager.switchEngine(AIEngineType.basic);
        _engineManager.setStrength(900);
        break;
      case AIDifficulty.medium:
        await _engineManager.switchEngine(AIEngineType.advanced);
        _engineManager.setStrength(1400);
        break;
      case AIDifficulty.hard:
        await _engineManager.switchEngine(AIEngineType.advanced);
        _engineManager.setStrength(1800);
        break;
      case AIDifficulty.expert:
        // Try to use Stockfish, fallback to advanced AI
        final success =
            await _engineManager.switchEngine(AIEngineType.stockfish);
        if (!success) {
          await _engineManager.switchEngine(AIEngineType.advanced);
        }
        _engineManager.setStrength(2200);
        break;
    }
  }

  /// Gets the best move for the current position
  Future<Move?> getBestMove(ChessEngine engine) async {
    // Use the engine manager to get the best move
    final move = await _engineManager.getBestMove(engine);

    // If engine manager fails, fallback to original logic
    if (move != null) {
      return move;
    }

    // Fallback to original implementation
    final legalMoves = engine.generateAllLegalMoves();
    if (legalMoves.isEmpty) return null;

    switch (difficulty) {
      case AIDifficulty.easy:
        return _getRandomMove(legalMoves);
      case AIDifficulty.medium:
        return _getBasicMove(engine, legalMoves);
      case AIDifficulty.hard:
        return _getMiniMaxMove(engine, legalMoves, 2);
      case AIDifficulty.expert:
        return _getMiniMaxMove(engine, legalMoves, 3);
    }
  }

  /// Returns a random legal move
  Move _getRandomMove(List<Move> legalMoves) {
    return legalMoves[_random.nextInt(legalMoves.length)];
  }

  /// Returns a move with basic evaluation
  Move _getBasicMove(ChessEngine engine, List<Move> legalMoves) {
    // Prioritize captures, checks, and central moves
    final captures = legalMoves.where((move) => move.isCapture).toList();
    final checks = legalMoves.where((move) => move.isCheck).toList();

    // Prefer checkmate moves
    final checkmates = legalMoves.where((move) => move.isCheckmate).toList();
    if (checkmates.isNotEmpty) {
      return checkmates.first;
    }

    // Prefer checks
    if (checks.isNotEmpty && _random.nextDouble() < 0.7) {
      return checks[_random.nextInt(checks.length)];
    }

    // Prefer captures
    if (captures.isNotEmpty && _random.nextDouble() < 0.6) {
      // Sort captures by piece value
      captures.sort((a, b) => _getPieceValue(b.capturedPiece!.type)
          .compareTo(_getPieceValue(a.capturedPiece!.type)));
      return captures.first;
    }

    // Otherwise, prefer central moves
    final centralMoves =
        legalMoves.where((move) => _isCentralSquare(move.to)).toList();
    if (centralMoves.isNotEmpty && _random.nextDouble() < 0.4) {
      return centralMoves[_random.nextInt(centralMoves.length)];
    }

    return _getRandomMove(legalMoves);
  }

  /// Returns the best move using minimax algorithm
  Move _getMiniMaxMove(ChessEngine engine, List<Move> legalMoves, int depth) {
    Move? bestMove;
    double bestScore = double.negativeInfinity;

    for (final move in legalMoves) {
      // Make a copy of the engine to test the move
      final testEngine = ChessEngine();
      testEngine.loadGameState(engine.gameState);

      try {
        testEngine.makeMove(move);
        final score = _minimax(testEngine, depth - 1, false,
            double.negativeInfinity, double.infinity);

        if (score > bestScore) {
          bestScore = score;
          bestMove = move;
        }
      } catch (e) {
        // Skip invalid moves
        continue;
      }
    }

    return bestMove ?? _getRandomMove(legalMoves);
  }

  /// Minimax algorithm with alpha-beta pruning
  double _minimax(ChessEngine engine, int depth, bool isMaximizing,
      double alpha, double beta) {
    if (depth == 0 || engine.gameState.isGameOver) {
      return _evaluatePosition(engine.gameState);
    }

    final legalMoves = engine.generateAllLegalMoves();

    if (isMaximizing) {
      double maxEval = double.negativeInfinity;

      for (final move in legalMoves) {
        final testEngine = ChessEngine();
        testEngine.loadGameState(engine.gameState);

        try {
          testEngine.makeMove(move);
          final eval = _minimax(testEngine, depth - 1, false, alpha, beta);
          maxEval = max(maxEval, eval);
          alpha = max(alpha, eval);

          if (beta <= alpha) break; // Alpha-beta pruning
        } catch (e) {
          continue;
        }
      }

      return maxEval;
    } else {
      double minEval = double.infinity;

      for (final move in legalMoves) {
        final testEngine = ChessEngine();
        testEngine.loadGameState(engine.gameState);

        try {
          testEngine.makeMove(move);
          final eval = _minimax(testEngine, depth - 1, true, alpha, beta);
          minEval = min(minEval, eval);
          beta = min(beta, eval);

          if (beta <= alpha) break; // Alpha-beta pruning
        } catch (e) {
          continue;
        }
      }

      return minEval;
    }
  }

  /// Evaluates the current position
  double _evaluatePosition(GameState gameState) {
    if (gameState.status == GameStatus.checkmate) {
      return gameState.currentPlayer == PieceColor.black ? 1000.0 : -1000.0;
    }

    if (gameState.status == GameStatus.stalemate ||
        gameState.status == GameStatus.draw) {
      return 0.0;
    }

    double score = 0.0;

    // Material evaluation
    score += _evaluateMaterial(gameState.board);

    // Position evaluation
    score += _evaluatePosition2(gameState.board);

    // King safety
    score += _evaluateKingSafety(gameState.board);

    // Mobility (simplified)
    score += _evaluateMobility(gameState.board);

    return score;
  }

  /// Evaluates material balance
  double _evaluateMaterial(Board board) {
    double score = 0.0;

    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = board.getPieceAt(row, col);
        if (piece != null) {
          final value = _getPieceValue(piece.type);
          score += piece.color == PieceColor.white ? value : -value;
        }
      }
    }

    return score;
  }

  /// Evaluates piece positions
  double _evaluatePosition2(Board board) {
    double score = 0.0;

    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = board.getPieceAt(row, col);
        if (piece != null) {
          final positionValue =
              _getPositionValue(piece, Position(row: row, col: col));
          score +=
              piece.color == PieceColor.white ? positionValue : -positionValue;
        }
      }
    }

    return score;
  }

  /// Evaluates king safety (simplified)
  double _evaluateKingSafety(Board board) {
    // Simplified king safety evaluation
    return 0.0;
  }

  /// Evaluates piece mobility (simplified)
  double _evaluateMobility(Board board) {
    // Simplified mobility evaluation
    return 0.0;
  }

  /// Gets the value of a piece type
  double _getPieceValue(PieceType type) {
    switch (type) {
      case PieceType.pawn:
        return 1.0;
      case PieceType.knight:
      case PieceType.bishop:
        return 3.0;
      case PieceType.rook:
        return 5.0;
      case PieceType.queen:
        return 9.0;
      case PieceType.king:
        return 0.0; // King value is handled separately
    }
  }

  /// Gets the positional value of a piece
  double _getPositionValue(ChessPiece piece, Position position) {
    // Simplified positional evaluation
    if (piece.type == PieceType.pawn) {
      // Pawns are better in the center and advanced
      final centerBonus = _isCentralSquare(position) ? 0.1 : 0.0;
      final advancementBonus = piece.color == PieceColor.white
          ? (7 - position.row) * 0.05
          : position.row * 0.05;
      return centerBonus + advancementBonus;
    }

    if (piece.type == PieceType.knight || piece.type == PieceType.bishop) {
      // Knights and bishops are better in the center
      return _isCentralSquare(position) ? 0.2 : 0.0;
    }

    return 0.0;
  }

  /// Checks if a square is central
  bool _isCentralSquare(Position position) {
    return (position.row >= 2 && position.row <= 5) &&
        (position.col >= 2 && position.col <= 5);
  }
}
