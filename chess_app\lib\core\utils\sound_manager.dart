import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

/// Manages sound effects for the chess app
class SoundManager {
  static final SoundManager _instance = SoundManager._internal();
  factory SoundManager() => _instance;
  SoundManager._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _soundEnabled = true;

  /// Enable or disable sound effects
  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }

  /// Check if sound is enabled
  bool get isSoundEnabled => _soundEnabled;

  /// Play move sound
  Future<void> playMoveSound() async {
    if (!_soundEnabled) return;
    
    try {
      // For now, use system sound since we don't have audio files
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play capture sound
  Future<void> playCaptureSound() async {
    if (!_soundEnabled) return;
    
    try {
      // Use a different system sound for captures
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play check sound
  Future<void> playCheckSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play checkmate sound
  Future<void> playCheckmateSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play game start sound
  Future<void> playGameStartSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play game end sound
  Future<void> playGameEndSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play button click sound
  Future<void> playButtonSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Play error sound
  Future<void> playErrorSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      // Silently handle errors
    }
  }

  /// Dispose resources
  void dispose() {
    _audioPlayer.dispose();
  }
}

/// Extension to easily play sounds from widgets
extension SoundExtension on SoundManager {
  /// Play sound based on move type
  Future<void> playMoveTypeSound(String moveType) async {
    switch (moveType) {
      case 'move':
        await playMoveSound();
        break;
      case 'capture':
        await playCaptureSound();
        break;
      case 'check':
        await playCheckSound();
        break;
      case 'checkmate':
        await playCheckmateSound();
        break;
      case 'error':
        await playErrorSound();
        break;
      default:
        await playMoveSound();
    }
  }
}
