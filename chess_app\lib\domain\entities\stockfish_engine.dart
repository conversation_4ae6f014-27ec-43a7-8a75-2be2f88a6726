import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'chess_ai_engine.dart';
import 'chess_engine.dart';
import 'move.dart';
import 'position.dart';
import 'piece.dart';
import 'game_state.dart';

/// Stockfish UCI engine wrapper
/// Note: This requires the Stockfish binary to be available
class StockfishEngine extends ChessAIEngine {
  Process? _process;
  StreamController<String>? _outputController;
  StreamSubscription? _outputSubscription;
  bool _isReady = false;
  int _strength = 1500;
  Completer<Move?>? _moveCompleter;
  Completer<PositionAnalysis>? _analysisCompleter;

  @override
  String get name => 'Stockfish';

  @override
  int get strength => _strength;

  /// Initialize the Stockfish engine
  Future<bool> initialize() async {
    try {
      // Try to start Stockfish process
      // Note: You need to have stockfish binary available
      _process = await Process.start('stockfish', []);

      _outputController = StreamController<String>.broadcast();

      _outputSubscription = _process!.stdout
          .transform(utf8.decoder)
          .transform(const LineSplitter())
          .listen(_handleOutput);

      // Send initial UCI commands
      await _sendCommand('uci');
      await _waitForReady();

      await _sendCommand('ucinewgame');
      await _setStrengthInternal();

      return true;
    } catch (e) {
      print('Failed to initialize Stockfish: $e');
      return false;
    }
  }

  @override
  void setStrength(int strength) {
    _strength = strength.clamp(800, 3000);
    if (_isReady) {
      _setStrengthInternal();
    }
  }

  Future<void> _setStrengthInternal() async {
    // Convert ELO to Stockfish skill level (0-20)
    final skillLevel = (((_strength - 800) / 2200) * 20).round().clamp(0, 20);

    await _sendCommand('setoption name Skill Level value $skillLevel');

    // For lower levels, add some randomness
    if (skillLevel < 15) {
      await _sendCommand('setoption name UCI_LimitStrength value true');
      await _sendCommand('setoption name UCI_Elo value $_strength');
    }
  }

  @override
  Future<Move?> getBestMove(ChessEngine engine, {Duration? timeLimit}) async {
    if (!_isReady || _process == null) {
      // Fallback to advanced AI if Stockfish is not available
      final fallbackAI = AdvancedChessAI();
      fallbackAI.setStrength(_strength);
      return await fallbackAI.getBestMove(engine, timeLimit: timeLimit);
    }

    try {
      _moveCompleter = Completer<Move?>();

      // Set position
      final fen = _gameStateToFEN(engine.gameState);
      await _sendCommand('position fen $fen');

      // Start search
      final timeMs = timeLimit?.inMilliseconds ?? 3000;
      await _sendCommand('go movetime $timeMs');

      return await _moveCompleter!.future;
    } catch (e) {
      print('Error getting best move from Stockfish: $e');
      return null;
    }
  }

  @override
  Future<PositionAnalysis> analyzePosition(ChessEngine engine) async {
    if (!_isReady || _process == null) {
      // Fallback to advanced AI
      final fallbackAI = AdvancedChessAI();
      fallbackAI.setStrength(_strength);
      return await fallbackAI.analyzePosition(engine);
    }

    try {
      _analysisCompleter = Completer<PositionAnalysis>();

      // Set position
      final fen = _gameStateToFEN(engine.gameState);
      await _sendCommand('position fen $fen');

      // Start analysis
      await _sendCommand('go depth 15');

      return await _analysisCompleter!.future;
    } catch (e) {
      print('Error analyzing position with Stockfish: $e');
      return PositionAnalysis(
        evaluation: 0,
        depth: 0,
        analysisTime: Duration.zero,
      );
    }
  }

  @override
  void stop() {
    if (_process != null) {
      _sendCommand('stop');
    }
  }

  @override
  void dispose() {
    stop();
    _outputSubscription?.cancel();
    _outputController?.close();
    _process?.kill();
    _process = null;
    _isReady = false;
  }

  Future<void> _sendCommand(String command) async {
    if (_process != null) {
      _process!.stdin.writeln(command);
      await _process!.stdin.flush();
    }
  }

  void _handleOutput(String line) {
    print('Stockfish: $line'); // Debug output

    if (line.startsWith('uciok')) {
      _isReady = true;
    } else if (line.startsWith('bestmove')) {
      _handleBestMove(line);
    } else if (line.startsWith('info')) {
      _handleInfo(line);
    }
  }

  void _handleBestMove(String line) {
    final parts = line.split(' ');
    if (parts.length >= 2) {
      final moveStr = parts[1];
      if (moveStr == '(none)') {
        _moveCompleter?.complete(null);
      } else {
        final move = _parseUCIMove(moveStr);
        _moveCompleter?.complete(move);
      }
    }
    _moveCompleter = null;
  }

  void _handleInfo(String line) {
    // Parse info line for analysis
    // This is a simplified version - full UCI parsing would be more complex
    if (line.contains('score cp')) {
      final regex = RegExp(r'score cp (-?\d+)');
      final match = regex.firstMatch(line);
      if (match != null) {
        final centipawns = int.parse(match.group(1)!);

        if (_analysisCompleter != null && !_analysisCompleter!.isCompleted) {
          _analysisCompleter!.complete(PositionAnalysis(
            evaluation: centipawns.toDouble(),
            depth: 1,
            analysisTime: const Duration(seconds: 1),
          ));
        }
      }
    }
  }

  Move? _parseUCIMove(String uciMove) {
    if (uciMove.length < 4) return null;

    try {
      final fromSquare = uciMove.substring(0, 2);
      final toSquare = uciMove.substring(2, 4);

      final from = Position.fromAlgebraic(fromSquare);
      final to = Position.fromAlgebraic(toSquare);

      // This is a simplified move creation - in a real implementation,
      // you'd need to determine the piece type, capture status, etc.
      // For now, we'll create a basic move structure
      return Move(
        from: from,
        to: to,
        piece: King(
            color:
                PieceColor.white), // Placeholder - needs proper piece detection
      );
    } catch (e) {
      print('Error parsing UCI move $uciMove: $e');
      return null;
    }
  }

  String _gameStateToFEN(GameState gameState) {
    // Convert game state to FEN notation
    // This is a simplified version - full FEN conversion would be more complex
    final buffer = StringBuffer();

    // Piece placement
    for (int row = 0; row < 8; row++) {
      int emptyCount = 0;
      for (int col = 0; col < 8; col++) {
        final piece = gameState.board.getPieceAt(row, col);
        if (piece == null) {
          emptyCount++;
        } else {
          if (emptyCount > 0) {
            buffer.write(emptyCount);
            emptyCount = 0;
          }
          buffer.write(_pieceToFEN(piece));
        }
      }
      if (emptyCount > 0) {
        buffer.write(emptyCount);
      }
      if (row < 7) buffer.write('/');
    }

    // Active color
    buffer.write(' ');
    buffer.write(gameState.currentPlayer == PieceColor.white ? 'w' : 'b');

    // Castling rights
    buffer.write(' ');
    String castling = '';
    if (gameState.whiteCanCastleKingside) castling += 'K';
    if (gameState.whiteCanCastleQueenside) castling += 'Q';
    if (gameState.blackCanCastleKingside) castling += 'k';
    if (gameState.blackCanCastleQueenside) castling += 'q';
    buffer.write(castling.isEmpty ? '-' : castling);

    // En passant
    buffer.write(' ');
    if (gameState.enPassantTarget != null) {
      buffer.write(gameState.enPassantTarget!.toAlgebraic());
    } else {
      buffer.write('-');
    }

    // Half-move clock and full-move number
    buffer.write(' ${gameState.halfMoveClock} ${gameState.fullMoveNumber}');

    return buffer.toString();
  }

  String _pieceToFEN(ChessPiece piece) {
    String symbol;
    switch (piece.type) {
      case PieceType.king:
        symbol = 'k';
        break;
      case PieceType.queen:
        symbol = 'q';
        break;
      case PieceType.rook:
        symbol = 'r';
        break;
      case PieceType.bishop:
        symbol = 'b';
        break;
      case PieceType.knight:
        symbol = 'n';
        break;
      case PieceType.pawn:
        symbol = 'p';
        break;
    }

    return piece.color == PieceColor.white ? symbol.toUpperCase() : symbol;
  }

  Future<void> _waitForReady() async {
    int attempts = 0;
    while (!_isReady && attempts < 50) {
      await Future.delayed(const Duration(milliseconds: 100));
      attempts++;
    }
  }
}
