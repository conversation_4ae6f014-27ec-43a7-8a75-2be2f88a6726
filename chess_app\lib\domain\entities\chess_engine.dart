import 'board.dart';
import 'game_state.dart';
import 'move.dart';
import 'piece.dart';
import 'position.dart';

/// Core chess engine that handles game logic and rules
class ChessEngine {
  GameState _gameState;

  ChessEngine() : _gameState = GameState.initial();

  /// Gets the current game state
  GameState get gameState => _gameState;

  /// Starts a new game
  void startNewGame() {
    _gameState = GameState.initial();
  }

  /// Loads a game state
  void loadGameState(GameState gameState) {
    _gameState = gameState;
  }

  /// Validates if a move is legal
  bool isValidMove(Move move) {
    // Check if it's the correct player's turn
    if (move.piece.color != _gameState.currentPlayer) {
      return false;
    }

    // Check if the piece is actually at the from position
    final pieceAtFrom = _gameState.board.getPiece(move.from);
    if (pieceAtFrom == null ||
        pieceAtFrom.runtimeType != move.piece.runtimeType) {
      return false;
    }

    // Get all legal moves for this piece
    final legalMoves = generateLegalMoves(move.from);

    // Check if the move is in the list of legal moves
    return legalMoves.any(
        (legalMove) => legalMove.to == move.to && legalMove.type == move.type);
  }

  /// Generates all legal moves for a piece at the given position
  List<Move> generateLegalMoves(Position from) {
    final piece = _gameState.board.getPiece(from);
    if (piece == null || piece.color != _gameState.currentPlayer) {
      return [];
    }

    final pseudoLegalMoves = _generatePseudoLegalMoves(from, piece);
    final legalMoves = <Move>[];

    // Filter out moves that would leave the king in check
    for (final move in pseudoLegalMoves) {
      final newGameState = _makeMove(move);
      if (!_isInCheck(newGameState.board, _gameState.currentPlayer)) {
        legalMoves.add(move);
      }
    }

    return legalMoves;
  }

  /// Generates all legal moves for the current player
  List<Move> generateAllLegalMoves() {
    final allMoves = <Move>[];
    final pieces = _gameState.board.getPiecesOfColor(_gameState.currentPlayer);

    for (final entry in pieces) {
      final position = entry.key;
      final moves = generateLegalMoves(position);
      allMoves.addAll(moves);
    }

    return allMoves;
  }

  /// Makes a move and updates the game state
  GameState makeMove(Move move) {
    if (!isValidMove(move)) {
      throw ArgumentError('Invalid move: $move');
    }

    _gameState = _makeMove(move);
    _gameState = _updateGameStatus();

    return _gameState;
  }

  /// Checks if the given color is in check
  bool isInCheck(PieceColor color) {
    return _isInCheck(_gameState.board, color);
  }

  /// Checks if the current player is in checkmate
  bool isCheckmate() {
    return _gameState.status == GameStatus.checkmate;
  }

  /// Checks if the game is in stalemate
  bool isStalemate() {
    return _gameState.status == GameStatus.stalemate;
  }

  /// Private method to generate pseudo-legal moves (doesn't check for check)
  List<Move> _generatePseudoLegalMoves(Position from, ChessPiece piece) {
    final moves = <Move>[];
    final basicMoves = piece.generateMoves(_gameState.board.squares, from);

    for (final to in basicMoves) {
      final targetPiece = _gameState.board.getPiece(to);
      final moveType = targetPiece != null ? MoveType.capture : MoveType.normal;

      moves.add(Move(
        from: from,
        to: to,
        piece: piece,
        capturedPiece: targetPiece,
        type: moveType,
      ));
    }

    // Add special moves
    moves.addAll(_generateCastlingMoves(from, piece));
    moves.addAll(_generateEnPassantMoves(from, piece));
    moves.addAll(_generatePromotionMoves(from, piece));

    return moves;
  }

  /// Generates castling moves
  List<Move> _generateCastlingMoves(Position from, ChessPiece piece) {
    final moves = <Move>[];

    if (piece is! King || piece.hasMoved) return moves;
    if (_isInCheck(_gameState.board, piece.color)) return moves;

    final row = piece.color == PieceColor.white ? 7 : 0;

    // Kingside castling
    if (_gameState.canCastleKingside(piece.color)) {
      if (_canCastle(from, Position(row: row, col: 6), true)) {
        moves.add(Move(
          from: from,
          to: Position(row: row, col: 6),
          piece: piece,
          type: MoveType.castling,
        ));
      }
    }

    // Queenside castling
    if (_gameState.canCastleQueenside(piece.color)) {
      if (_canCastle(from, Position(row: row, col: 2), false)) {
        moves.add(Move(
          from: from,
          to: Position(row: row, col: 2),
          piece: piece,
          type: MoveType.castling,
        ));
      }
    }

    return moves;
  }

  /// Generates en passant moves
  List<Move> _generateEnPassantMoves(Position from, ChessPiece piece) {
    final moves = <Move>[];

    if (piece is! Pawn || _gameState.enPassantTarget == null) return moves;

    final direction = piece.color == PieceColor.white ? -1 : 1;
    final enPassantPos = _gameState.enPassantTarget!;

    // Check if pawn can capture en passant
    if ((from.col - enPassantPos.col).abs() == 1 &&
        from.row + direction == enPassantPos.row) {
      final capturedPawnPos = Position(row: from.row, col: enPassantPos.col);
      final capturedPawn = _gameState.board.getPiece(capturedPawnPos);

      moves.add(Move(
        from: from,
        to: enPassantPos,
        piece: piece,
        capturedPiece: capturedPawn,
        type: MoveType.enPassant,
      ));
    }

    return moves;
  }

  /// Generates promotion moves
  List<Move> _generatePromotionMoves(Position from, ChessPiece piece) {
    final moves = <Move>[];

    if (piece is! Pawn) return moves;

    final direction = piece.color == PieceColor.white ? -1 : 1;
    final promotionRow = piece.color == PieceColor.white ? 0 : 7;

    // Check if pawn is about to promote
    if (from.row + direction == promotionRow) {
      final basicMoves = piece.generateMoves(_gameState.board.squares, from);

      for (final to in basicMoves) {
        final targetPiece = _gameState.board.getPiece(to);

        // Generate moves for each promotion piece
        for (final promotionPiece in [
          PieceType.queen,
          PieceType.rook,
          PieceType.bishop,
          PieceType.knight,
        ]) {
          moves.add(Move(
            from: from,
            to: to,
            piece: piece,
            capturedPiece: targetPiece,
            type: MoveType.promotion,
            promotionPiece: promotionPiece,
          ));
        }
      }
    }

    return moves;
  }

  /// Private method to make a move without validation
  GameState _makeMove(Move move) {
    var newBoard = _gameState.board;
    Position? newEnPassantTarget;
    var newHalfMoveClock = _gameState.halfMoveClock + 1;
    var newFullMoveNumber = _gameState.fullMoveNumber;

    // Handle different move types
    switch (move.type) {
      case MoveType.normal:
      case MoveType.capture:
        newBoard = newBoard.makeMove(move.from, move.to);
        break;

      case MoveType.castling:
        newBoard = _handleCastling(newBoard, move);
        break;

      case MoveType.enPassant:
        newBoard = _handleEnPassant(newBoard, move);
        break;

      case MoveType.promotion:
        final promotedPiece =
            _createPromotedPiece(move.piece.color, move.promotionPiece!);
        newBoard = newBoard.makeMove(move.from, move.to,
            promotionPiece: promotedPiece);
        break;
    }

    // Update en passant target for pawn double moves
    if (move.piece is Pawn && (move.to.row - move.from.row).abs() == 2) {
      final direction = move.piece.color == PieceColor.white ? 1 : -1;
      newEnPassantTarget =
          Position(row: move.from.row + direction, col: move.from.col);
    }

    // Reset half-move clock for pawn moves and captures
    if (move.piece is Pawn || move.isCapture) {
      newHalfMoveClock = 0;
    }

    // Increment full move number after black's move
    if (_gameState.currentPlayer == PieceColor.black) {
      newFullMoveNumber++;
    }

    // Update move history
    final newMoveHistory = List<Move>.from(_gameState.moveHistory)..add(move);

    // Switch current player
    final newCurrentPlayer = _gameState.currentPlayer == PieceColor.white
        ? PieceColor.black
        : PieceColor.white;

    // Update castling rights
    var newGameState = _gameState.copyWith(
      board: newBoard,
      currentPlayer: newCurrentPlayer,
      moveHistory: newMoveHistory,
      enPassantTarget: newEnPassantTarget,
      halfMoveClock: newHalfMoveClock,
      fullMoveNumber: newFullMoveNumber,
    );

    newGameState = newGameState.updateCastlingRights(move);

    return newGameState;
  }

  /// Checks if a color is in check
  bool _isInCheck(Board board, PieceColor color) {
    final kingPosition = board.findKing(color);
    if (kingPosition == null) return false;

    final opponentColor =
        color == PieceColor.white ? PieceColor.black : PieceColor.white;
    final opponentPieces = board.getPiecesOfColor(opponentColor);

    for (final entry in opponentPieces) {
      final piece = entry.value;
      final position = entry.key;
      final moves = piece.generateMoves(board.squares, position);

      if (moves.contains(kingPosition)) {
        return true;
      }
    }

    return false;
  }

  /// Updates the game status based on current position
  GameState _updateGameStatus() {
    final legalMoves = generateAllLegalMoves();
    final inCheck = _isInCheck(_gameState.board, _gameState.currentPlayer);

    GameStatus newStatus;
    if (legalMoves.isEmpty) {
      newStatus = inCheck ? GameStatus.checkmate : GameStatus.stalemate;
    } else if (inCheck) {
      newStatus = GameStatus.check;
    } else if (_gameState.halfMoveClock >= 100) {
      // 50-move rule
      newStatus = GameStatus.draw;
    } else if (_isThreefoldRepetition()) {
      newStatus = GameStatus.draw;
    } else {
      newStatus = GameStatus.playing;
    }

    return _gameState.copyWith(status: newStatus);
  }

  /// Checks for threefold repetition
  bool _isThreefoldRepetition() {
    // Simplified implementation - in a real game, you'd need to track position hashes
    return false;
  }

  /// Handles castling move
  Board _handleCastling(Board board, Move move) {
    final row = move.piece.color == PieceColor.white ? 7 : 0;
    final isKingside = move.to.col == 6;

    // Move king
    var newBoard = board.makeMove(move.from, move.to);

    // Move rook
    if (isKingside) {
      // Kingside castling
      newBoard = newBoard.makeMove(
        Position(row: row, col: 7),
        Position(row: row, col: 5),
      );
    } else {
      // Queenside castling
      newBoard = newBoard.makeMove(
        Position(row: row, col: 0),
        Position(row: row, col: 3),
      );
    }

    return newBoard;
  }

  /// Handles en passant move
  Board _handleEnPassant(Board board, Move move) {
    // Move the pawn
    var newBoard = board.makeMove(move.from, move.to);

    // Remove the captured pawn
    final capturedPawnRow = move.from.row;
    final capturedPawnCol = move.to.col;
    final newSquares =
        newBoard.squares.map((row) => List<ChessPiece?>.from(row)).toList();
    newSquares[capturedPawnRow][capturedPawnCol] = null;

    return Board(newSquares);
  }

  /// Creates a promoted piece
  ChessPiece _createPromotedPiece(PieceColor color, PieceType type) {
    switch (type) {
      case PieceType.queen:
        return Queen(color: color, hasMoved: true);
      case PieceType.rook:
        return Rook(color: color, hasMoved: true);
      case PieceType.bishop:
        return Bishop(color: color, hasMoved: true);
      case PieceType.knight:
        return Knight(color: color, hasMoved: true);
      default:
        throw ArgumentError('Invalid promotion piece type: $type');
    }
  }

  /// Checks if castling is possible
  bool _canCastle(Position kingFrom, Position kingTo, bool isKingside) {
    final row = kingFrom.row;
    final startCol = isKingside ? 5 : 3;
    final endCol = isKingside ? 6 : 1;

    // Check if squares between king and rook are empty
    for (int col = startCol; col <= endCol; col++) {
      if (col != kingFrom.col &&
          !_gameState.board.isSquareEmpty(Position(row: row, col: col))) {
        return false;
      }
    }

    // Check if king passes through or ends up in check
    for (int col = kingFrom.col;
        col != kingTo.col + (isKingside ? 1 : -1);
        col += isKingside ? 1 : -1) {
      final testBoard =
          _gameState.board.makeMove(kingFrom, Position(row: row, col: col));
      if (_isInCheck(testBoard, _gameState.currentPlayer)) {
        return false;
      }
    }

    return true;
  }
}
