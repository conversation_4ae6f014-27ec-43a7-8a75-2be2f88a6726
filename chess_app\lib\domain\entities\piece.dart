import 'package:equatable/equatable.dart';
import 'position.dart';

/// Enum for piece colors
enum PieceColor { white, black }

/// Enum for piece types
enum PieceType { king, queen, rook, bishop, knight, pawn }

/// Abstract base class for chess pieces
abstract class ChessPiece extends Equatable {
  final PieceColor color;
  final PieceType type;
  final bool hasMoved;

  const ChessPiece({
    required this.color,
    required this.type,
    this.hasMoved = false,
  });

  /// Creates a copy of the piece with updated properties
  Chess<PERSON>iece copyWith({bool? hasMoved});

  /// Generates all possible moves for this piece from the given position
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from);

  /// Gets the symbol representation of the piece
  String get symbol;

  /// Gets the asset path for the piece image
  String get assetPath {
    final colorName = color == PieceColor.white ? 'white' : 'black';
    final typeName = type.name.toLowerCase();
    return 'assets/chess_pieces/${colorName}_$typeName.png';
  }

  @override
  List<Object> get props => [color, type, hasMoved];
}

/// King piece implementation
class King extends ChessPiece {
  const King({required PieceColor color, bool hasMoved = false})
      : super(color: color, type: PieceType.king, hasMoved: hasMoved);

  @override
  King copyWith({bool? hasMoved}) {
    return King(color: color, hasMoved: hasMoved ?? this.hasMoved);
  }

  @override
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from) {
    final moves = <Position>[];

    // King moves one square in any direction
    for (int rowOffset = -1; rowOffset <= 1; rowOffset++) {
      for (int colOffset = -1; colOffset <= 1; colOffset++) {
        if (rowOffset == 0 && colOffset == 0) continue;

        final to = from.offset(rowOffset, colOffset);
        if (to.isValid) {
          final targetPiece = board[to.row][to.col];
          if (targetPiece == null || targetPiece.color != color) {
            moves.add(to);
          }
        }
      }
    }

    return moves;
  }

  @override
  String get symbol => color == PieceColor.white ? '♔' : '♚';
}

/// Queen piece implementation
class Queen extends ChessPiece {
  const Queen({required PieceColor color, bool hasMoved = false})
      : super(color: color, type: PieceType.queen, hasMoved: hasMoved);

  @override
  Queen copyWith({bool? hasMoved}) {
    return Queen(color: color, hasMoved: hasMoved ?? this.hasMoved);
  }

  @override
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from) {
    final moves = <Position>[];

    // Queen moves like rook + bishop
    final directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1], // Rook directions
      [-1, -1], [-1, 1], [1, -1], [1, 1], // Bishop directions
    ];

    for (final direction in directions) {
      for (int i = 1; i < 8; i++) {
        final to = from.offset(direction[0] * i, direction[1] * i);
        if (!to.isValid) break;

        final targetPiece = board[to.row][to.col];
        if (targetPiece == null) {
          moves.add(to);
        } else {
          if (targetPiece.color != color) {
            moves.add(to);
          }
          break;
        }
      }
    }

    return moves;
  }

  @override
  String get symbol => color == PieceColor.white ? '♕' : '♛';
}

/// Rook piece implementation
class Rook extends ChessPiece {
  const Rook({required PieceColor color, bool hasMoved = false})
      : super(color: color, type: PieceType.rook, hasMoved: hasMoved);

  @override
  Rook copyWith({bool? hasMoved}) {
    return Rook(color: color, hasMoved: hasMoved ?? this.hasMoved);
  }

  @override
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from) {
    final moves = <Position>[];

    // Rook moves horizontally and vertically
    final directions = [
      [-1, 0],
      [1, 0],
      [0, -1],
      [0, 1]
    ];

    for (final direction in directions) {
      for (int i = 1; i < 8; i++) {
        final to = from.offset(direction[0] * i, direction[1] * i);
        if (!to.isValid) break;

        final targetPiece = board[to.row][to.col];
        if (targetPiece == null) {
          moves.add(to);
        } else {
          if (targetPiece.color != color) {
            moves.add(to);
          }
          break;
        }
      }
    }

    return moves;
  }

  @override
  String get symbol => color == PieceColor.white ? '♖' : '♜';
}

/// Bishop piece implementation
class Bishop extends ChessPiece {
  const Bishop({required PieceColor color, bool hasMoved = false})
      : super(color: color, type: PieceType.bishop, hasMoved: hasMoved);

  @override
  Bishop copyWith({bool? hasMoved}) {
    return Bishop(color: color, hasMoved: hasMoved ?? this.hasMoved);
  }

  @override
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from) {
    final moves = <Position>[];

    // Bishop moves diagonally
    final directions = [
      [-1, -1],
      [-1, 1],
      [1, -1],
      [1, 1]
    ];

    for (final direction in directions) {
      for (int i = 1; i < 8; i++) {
        final to = from.offset(direction[0] * i, direction[1] * i);
        if (!to.isValid) break;

        final targetPiece = board[to.row][to.col];
        if (targetPiece == null) {
          moves.add(to);
        } else {
          if (targetPiece.color != color) {
            moves.add(to);
          }
          break;
        }
      }
    }

    return moves;
  }

  @override
  String get symbol => color == PieceColor.white ? '♗' : '♝';
}

/// Knight piece implementation
class Knight extends ChessPiece {
  const Knight({required PieceColor color, bool hasMoved = false})
      : super(color: color, type: PieceType.knight, hasMoved: hasMoved);

  @override
  Knight copyWith({bool? hasMoved}) {
    return Knight(color: color, hasMoved: hasMoved ?? this.hasMoved);
  }

  @override
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from) {
    final moves = <Position>[];

    // Knight moves in L-shape
    final knightMoves = [
      [-2, -1],
      [-2, 1],
      [-1, -2],
      [-1, 2],
      [1, -2],
      [1, 2],
      [2, -1],
      [2, 1],
    ];

    for (final move in knightMoves) {
      final to = from.offset(move[0], move[1]);
      if (to.isValid) {
        final targetPiece = board[to.row][to.col];
        if (targetPiece == null || targetPiece.color != color) {
          moves.add(to);
        }
      }
    }

    return moves;
  }

  @override
  String get symbol => color == PieceColor.white ? '♘' : '♞';
}

/// Pawn piece implementation
class Pawn extends ChessPiece {
  const Pawn({required PieceColor color, bool hasMoved = false})
      : super(color: color, type: PieceType.pawn, hasMoved: hasMoved);

  @override
  Pawn copyWith({bool? hasMoved}) {
    return Pawn(color: color, hasMoved: hasMoved ?? this.hasMoved);
  }

  @override
  List<Position> generateMoves(List<List<ChessPiece?>> board, Position from) {
    final moves = <Position>[];
    final direction = color == PieceColor.white ? -1 : 1;

    // Forward move
    final oneForward = from.offset(direction, 0);
    if (oneForward.isValid && board[oneForward.row][oneForward.col] == null) {
      moves.add(oneForward);

      // Two squares forward from starting position
      if (!hasMoved) {
        final twoForward = from.offset(direction * 2, 0);
        if (twoForward.isValid &&
            board[twoForward.row][twoForward.col] == null) {
          moves.add(twoForward);
        }
      }
    }

    // Diagonal captures
    for (final colOffset in [-1, 1]) {
      final capturePos = from.offset(direction, colOffset);
      if (capturePos.isValid) {
        final targetPiece = board[capturePos.row][capturePos.col];
        if (targetPiece != null && targetPiece.color != color) {
          moves.add(capturePos);
        }
      }
    }

    return moves;
  }

  @override
  String get symbol => color == PieceColor.white ? '♙' : '♟';
}
