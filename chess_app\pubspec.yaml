name: chess_app
description: A complete chess application for Android and iOS
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.0.15

  # Network
  dio: ^5.3.2
  web_socket_channel: ^2.4.0



  # UI
  flutter_svg: ^2.0.7
  cached_network_image: ^3.2.3
  animations: ^2.0.7
  flutter_animate: ^4.2.0

  # Audio
  audioplayers: ^5.0.0

  # Utils
  uuid: ^4.0.0
  intl: ^0.18.1

  # Chess specific
  chess: ^0.8.1  # For move validation and PGN support

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  hive_generator: ^2.0.0
  build_runner: ^2.4.6
  mockito: ^5.4.2

flutter:
  uses-material-design: true

  assets:
    - assets/chess_pieces/
    - assets/sounds/
    - assets/images/
