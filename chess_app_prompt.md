# Complete Chess App Development Prompt for Google Play Store

## Project Overview
Develop a fully functional chess application for Android devices that can be published on Google Play Store. The app should provide a complete chess gaming experience with modern UI/UX design, multiple game modes, and robust functionality.

## Technical Requirements

### Platform & Framework
- **Target Platform**: Android (API Level 21+ / Android 5.0+) with iOS compatibility
- **Framework**: Flutter (Latest stable version 3.16+)
- **Language**: Dart
- **Architecture**: Clean Architecture with BLoC pattern
- **State Management**: Flutter BLoC or Riverpod

### Core Features Required

#### 1. Game Engine
- Complete chess rule implementation including:
  - Standard piece movements (<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Pawn)
  - Special moves: Castling, En passant, Pawn promotion
  - Check and checkmate detection
  - Stalemate detection
  - Threefold repetition rule
  - 50-move rule
  - Draw conditions

#### 2. Game Modes
- **Human vs Human**: Local multiplayer on same device
- **Human vs AI**: Multiple difficulty levels (Easy, Medium, Hard, Expert)
- **Online Multiplayer**: Real-time chess with other players
- **Puzzle Mode**: Daily chess puzzles and tactics training
- **Analysis Mode**: Review and analyze completed games

#### 3. User Interface
- Clean, modern Material Design UI
- Responsive design for various screen sizes
- Dark/Light theme support
- Intuitive piece movement (drag & drop, tap to move)
- Visual indicators for:
  - Valid moves
  - Check/checkmate status
  - Last move highlight
  - Captured pieces display

#### 4. User Management
- User registration and authentication
- Player profiles and statistics
- Rating system (ELO-based)
- Game history and saved games
- Achievement system

#### 5. Additional Features
- Move notation (algebraic notation)
- Game timer with various time controls
- Sound effects and animations
- Offline mode capability
- Game sharing and export (PGN format)
- Hint system for beginners

## Asset Integration

### Chess Pieces Assets
The following chess piece images are available at the specified locations:

```
assets/chess_pieces/
├── black_bishop.png
├── black_king.png
├── black_knight.png
├── black_pawn.png
├── black_queen.png
├── black_rook.png
├── White_bishop.png
├── white_king.png
├── white_knight.png
├── white_pawn.png
├── white_queen.png
└── white_rook.png
```

### 8. Asset Integration in Flutter
```dart
class PieceAssets {
  static const String _basePath = 'assets/chess_pieces/';
  
  static String getPieceAsset(Color color, PieceType type) {
    final colorName = color == Color.white ? 'white' : 'black';
    final typeName = type.name.toLowerCase();
    return '$_basePath${colorName}_$typeName.png';
  }
  
  static const Map<String, String> pieceAssets = {
    'white_king': '${_basePath}white_king.png',
    'white_queen': '${_basePath}white_queen.png',
    'white_rook': '${_basePath}white_rook.png',
    'white_bishop': '${_basePath}White_bishop.png', // Note: Capital W
    'white_knight': '${_basePath}white_knight.png',
    'white_pawn': '${_basePath}white_pawn.png',
    'black_king': '${_basePath}black_king.png',
    'black_queen': '${_basePath}black_queen.png',
    'black_rook': '${_basePath}black_rook.png',
    'black_bishop': '${_basePath}black_bishop.png',
    'black_knight': '${_basePath}black_knight.png',
    'black_pawn': '${_basePath}black_pawn.png',
  };
}

// Usage in widgets
Widget buildPieceWidget(Piece piece) {
  return Image.asset(
    PieceAssets.getPieceAsset(piece.color, piece.type),
    width: 48,
    height: 48,
    fit: BoxFit.contain,
  );
}
```

### 9. Required Permissions (android/app/src/main/AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## Technical Implementation Guidelines

### 1. Chess Engine Implementation
```dart
// Core chess engine structure
class ChessEngine {
  late Board board;
  late GameState gameState;
  late MoveValidator moveValidator;
  late List<Move> moveHistory;
  
  bool isValidMove(Move move) { }
  void makeMove(Move move) { }
  bool isInCheck(Color color) { }
  bool isCheckmate(Color color) { }
  bool isStalemate(Color color) { }
  List<Move> generateLegalMoves(Color color) { }
}

class Board {
  List<List<Piece?>> squares = List.generate(8, (_) => List.filled(8, null));
  
  void setupInitialPosition() { }
  Piece? getPiece(int row, int col) { }
  void setPiece(int row, int col, Piece? piece) { }
  bool isSquareEmpty(int row, int col) { }
}

abstract class Piece {
  final Color color;
  final PieceType type;
  bool hasMoved;
  
  Piece(this.color, this.type, {this.hasMoved = false});
  
  List<Move> generateMoves(Board board, int row, int col);
  String get symbol;
}
```

### 2. Flutter Project Structure
```
lib/
├── main.dart
├── core/
│   ├── constants/
│   ├── errors/
│   ├── utils/
│   └── theme/
├── data/
│   ├── datasources/
│   ├── models/
│   └── repositories/
├── domain/
│   ├── entities/
│   ├── repositories/
│   └── usecases/
├── presentation/
│   ├── bloc/
│   ├── pages/
│   ├── widgets/
│   └── utils/
└── injection_container.dart

assets/
├── chess_pieces/
│   ├── black_bishop.png
│   ├── black_king.png
│   ├── black_knight.png
│   ├── black_pawn.png
│   ├── black_queen.png
│   ├── black_rook.png
│   ├── white_bishop.png
│   ├── white_king.png
│   ├── white_knight.png
│   ├── white_pawn.png
│   ├── white_queen.png
│   └── white_rook.png
├── sounds/
└── images/
```

### 3. BLoC State Management Implementation
```dart
// Game BLoC
class GameBloc extends Bloc<GameEvent, GameState> {
  final ChessEngine chessEngine;
  final AIPlayer aiPlayer;
  
  GameBloc({required this.chessEngine, required this.aiPlayer}) : super(GameInitial()) {
    on<MovePieceEvent>(_onMovePiece);
    on<StartNewGameEvent>(_onStartNewGame);
    on<UndoMoveEvent>(_onUndoMove);
    on<AIMovePieceEvent>(_onAIMove);
  }
  
  void _onMovePiece(MovePieceEvent event, Emitter<GameState> emit) async {
    // Handle piece movement logic
  }
}

// Game States
abstract class GameState extends Equatable {
  const GameState();
  
  @override
  List<Object> get props => [];
}

class GameInitial extends GameState {}

class GameInProgress extends GameState {
  final Board board;
  final Color currentPlayer;
  final List<Move> moveHistory;
  final GameStatus status;
  
  const GameInProgress({
    required this.board,
    required this.currentPlayer,
    required this.moveHistory,
    required this.status,
  });
  
  @override
  List<Object> get props => [board, currentPlayer, moveHistory, status];
}

class GameFinished extends GameState {
  final GameResult result;
  final String reason;
  
  const GameFinished({required this.result, required this.reason});
  
  @override
  List<Object> get props => [result, reason];
}

// Game Events
abstract class GameEvent extends Equatable {
  const GameEvent();
  
  @override
  List<Object> get props => [];
}

class MovePieceEvent extends GameEvent {
  final Move move;
  
  const MovePieceEvent({required this.move});
  
  @override
  List<Object> get props => [move];
}
```

### 4. Chess Board UI Implementation
```dart
class ChessBoardWidget extends StatefulWidget {
  final Board board;
  final Function(Move) onMovePiece;
  final List<Position> highlightedSquares;
  
  const ChessBoardWidget({
    Key? key,
    required this.board,
    required this.onMovePiece,
    this.highlightedSquares = const [],
  }) : super(key: key);
  
  @override
  State<ChessBoardWidget> createState() => _ChessBoardWidgetState();
}

class _ChessBoardWidgetState extends State<ChessBoardWidget> {
  Position? selectedSquare;
  List<Position> validMoves = [];
  
  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.brown[800]!, width: 2),
        ),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
          ),
          itemCount: 64,
          itemBuilder: (context, index) {
            final row = index ~/ 8;
            final col = index % 8;
            final position = Position(row: row, col: col);
            
            return GestureDetector(
              onTap: () => _onSquareTapped(position),
              child: ChessSquareWidget(
                position: position,
                piece: widget.board.getPiece(row, col),
                isSelected: selectedSquare == position,
                isValidMove: validMoves.contains(position),
                isHighlighted: widget.highlightedSquares.contains(position),
              ),
            );
          },
        ),
      ),
    );
  }
  
  void _onSquareTapped(Position position) {
    // Handle square tap logic
  }
}

class ChessSquareWidget extends StatelessWidget {
  final Position position;
  final Piece? piece;
  final bool isSelected;
  final bool isValidMove;
  final bool isHighlighted;
  
  const ChessSquareWidget({
    Key? key,
    required this.position,
    this.piece,
    this.isSelected = false,
    this.isValidMove = false,
    this.isHighlighted = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final isLightSquare = (position.row + position.col) % 2 == 0;
    
    return Container(
      decoration: BoxDecoration(
        color: _getSquareColor(isLightSquare),
        border: isSelected ? Border.all(color: Colors.yellow, width: 3) : null,
      ),
      child: Stack(
        children: [
          if (isValidMove) _buildValidMoveIndicator(),
          if (piece != null) _buildPieceWidget(),
          if (isHighlighted) _buildHighlightOverlay(),
        ],
      ),
    );
  }
  
  Color _getSquareColor(bool isLight) {
    if (isLight) {
      return Colors.grey[300]!;
    } else {
      return Colors.grey[700]!;
    }
  }
  
  Widget _buildPieceWidget() {
    return Center(
      child: Image.asset(
        'assets/chess_pieces/${piece!.color.name.toLowerCase()}_${piece!.type.name.toLowerCase()}.png',
        width: 40,
        height: 40,
      ),
    );
  }
  
  Widget _buildValidMoveIndicator() {
    return Center(
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.6),
          shape: BoxShape.circle,
        ),
      ),
    );
  }
  
  Widget _buildHighlightOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.3),
      ),
    );
  }
}
```

### 5. Required Dependencies (pubspec.yaml)
```yaml
name: chess_app
description: A complete chess application for Android and iOS
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.0.15
  
  # Network
  dio: ^5.3.2
  web_socket_channel: ^2.4.0
  
  # Firebase (for multiplayer)
  firebase_core: ^2.15.1
  firebase_auth: ^4.7.3
  cloud_firestore: ^4.9.1
  
  # UI
  flutter_svg: ^2.0.7
  cached_network_image: ^3.2.3
  animations: ^2.0.7
  flutter_animate: ^4.2.0
  
  # Audio
  audioplayers: ^5.0.0
  
  # Utils
  uuid: ^4.0.0
  intl: ^0.18.1
  
  # Chess specific
  chess: ^0.6.2  # For move validation and PGN support

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  hive_generator: ^2.0.0
  build_runner: ^2.4.6
  mockito: ^5.4.2

flutter:
  uses-material-design: true
  
  assets:
    - assets/chess_pieces/
    - assets/sounds/
    - assets/images/
  
  fonts:
    - family: ChessFont
      fonts:
        - asset: assets/fonts/chess_font.ttf
```

## Google Play Store Requirements

### 1. App Metadata
- **App Name**: [Your Chess App Name]
- **Package Name**: com.yourcompany.chess
- **Version Code**: Start with 1
- **Target SDK**: Latest available (API 34+)
- **Minimum SDK**: API 21 (Android 5.0)

### 7. Firebase Integration for Multiplayer
```dart
class MultiplayerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  Stream<GameState> watchGame(String gameId) {
    return _firestore
        .collection('games')
        .doc(gameId)
        .snapshots()
        .map((snapshot) => GameState.fromMap(snapshot.data()!));
  }
  
  Future<void> makeMove(String gameId, Move move) async {
    await _firestore.collection('games').doc(gameId).update({
      'moves': FieldValue.arrayUnion([move.toMap()]),
      'currentPlayer': move.color == Color.white ? 'black' : 'white',
      'lastMove': move.toMap(),
      'timestamp': FieldValue.serverTimestamp(),
    });
  }
  
  Future<String> createGame(String opponentId) async {
    final gameDoc = await _firestore.collection('games').add({
      'whitePlayer': _auth.currentUser!.uid,
      'blackPlayer': opponentId,
      'moves': [],
      'currentPlayer': 'white',
      'status': 'active',
      'createdAt': FieldValue.serverTimestamp(),
    });
    return gameDoc.id;
  }
  
  Future<void> joinMatchmaking() async {
    await _firestore.collection('matchmaking').doc(_auth.currentUser!.uid).set({
      'userId': _auth.currentUser!.uid,
      'timestamp': FieldValue.serverTimestamp(),
      'rating': await _getUserRating(),
    });
  }
  
  Future<int> _getUserRating() async {
    final userDoc = await _firestore.collection('users').doc(_auth.currentUser!.uid).get();
    return userDoc.data()?['rating'] ?? 1200;
  }
}
```

### 3. Content Rating
- Target age group: Everyone
- Include content rating questionnaire responses
- Ensure no inappropriate content

### 4. App Bundle Requirements
- Generate signed AAB (Android App Bundle)
- Include all required resources and assets
- Optimize for app size (under 100MB recommended)

## Quality Assurance & Testing

### Testing Requirements
- Unit tests for chess engine logic
- UI automation tests
- Performance testing (smooth 60fps gameplay)
- Memory leak detection
- Network connectivity testing
- Cross-device compatibility testing

## Flutter Performance Optimization
- Use `const` constructors wherever possible
- Implement proper widget disposal in StatefulWidgets
- Use `ListView.builder` for large lists
- Optimize images with `flutter_svg` for vector graphics
- Implement proper state management with BLoC
- Use `Isolate` for heavy computations (AI calculations)
- Implement proper error boundaries
- Use `RepaintBoundary` for complex animations
- Optimize asset loading and caching

## Flutter Testing Strategy
```dart
// Unit tests for chess engine
testWidgets('Chess engine validates moves correctly', (tester) async {
  final engine = ChessEngine();
  final move = Move(from: Position(1, 4), to: Position(3, 4));
  expect(engine.isValidMove(move), true);
});

// Widget tests for UI components
testWidgets('Chess board displays correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: ChessBoardWidget(
        board: Board(),
        onMovePiece: (move) {},
      ),
    ),
  );
  
  expect(find.byType(ChessSquareWidget), findsNWidgets(64));
});

// Integration tests for game flow
testWidgets('Complete game flow works', (tester) async {
  // Test full game scenario
});
```

## Security Considerations
- Secure user authentication
- Data encryption for sensitive information
- Input validation and sanitization
- Protection against common vulnerabilities
- Secure API communications

## Monetization Strategy (Optional)
- Free version with ads
- Premium version without ads
- In-app purchases for themes/pieces
- Subscription for advanced features
- Tournament entry fees

## Flutter-Specific Development Timeline
- **Week 1-2**: Flutter project setup, core chess engine, and basic UI widgets
- **Week 3-4**: Complete BLoC implementation, AI integration, and advanced UI components
- **Week 5-6**: Firebase integration, multiplayer functionality, and user authentication
- **Week 7-8**: Polish, animations, sound effects, and comprehensive testing
- **Week 9-10**: Play Store optimization, APK generation, and submission preparation

## Flutter Deliverables
1. Complete Flutter project source code with proper architecture
2. Compiled APK and AAB files ready for Play Store
3. Flutter-specific technical documentation
4. BLoC pattern implementation guide
5. Firebase configuration and setup guide
6. Asset integration and optimization guide
7. User manual and in-app help system
8. Privacy policy and terms of service
9. Play Store listing materials (screenshots, descriptions, metadata)
10. CI/CD pipeline setup for automated builds

### 10. Main Application Structure
```dart
// main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Firebase.initializeApp();
  await Hive.initFlutter();
  
  // Register Hive adapters
  Hive.registerAdapter(GameAdapter());
  Hive.registerAdapter(MoveAdapter());
  
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => GameBloc()),
        BlocProvider(create: (context) => AuthBloc()),
        BlocProvider(create: (context) => MultiplayerBloc()),
      ],
      child: MaterialApp(
        title: 'Chess Master',
        theme: ThemeData(
          primarySwatch: Colors.brown,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        darkTheme: ThemeData.dark().copyWith(
          primaryColor: Colors.brown,
        ),
        home: SplashScreen(),
        routes: {
          '/home': (context) => HomeScreen(),
          '/game': (context) => GameScreen(),
          '/multiplayer': (context) => MultiplayerScreen(),
          '/puzzle': (context) => PuzzleScreen(),
          '/settings': (context) => SettingsScreen(),
        },
      ),
    );
  }
}

// Game Screen
class GameScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chess Game'),
        actions: [
          IconButton(
            icon: Icon(Icons.undo),
            onPressed: () => context.read<GameBloc>().add(UndoMoveEvent()),
          ),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () => Navigator.pushNamed(context, '/settings'),
          ),
        ],
      ),
      body: BlocBuilder<GameBloc, GameState>(
        builder: (context, state) {
          if (state is GameInitial) {
            return Center(child: CircularProgressIndicator());
          } else if (state is GameInProgress) {
            return Column(
              children: [
                GameInfoWidget(gameState: state),
                Expanded(
                  child: ChessBoardWidget(
                    board: state.board,
                    onMovePiece: (move) => context.read<GameBloc>().add(MovePieceEvent(move: move)),
                  ),
                ),
                GameControlsWidget(),
              ],
            );
          } else if (state is GameFinished) {
            return GameEndScreen(result: state.result, reason: state.reason);
          }
          return SizedBox.shrink();
        },
      ),
    );
  }
}
```

## Post-Launch Considerations
- Regular updates and bug fixes
- User feedback integration
- Performance monitoring
- Analytics implementation
- Community building features

## Success Metrics
- Play Store rating: 4.0+ stars
- Download targets: 10K+ in first month
- User retention: 30% after 7 days
- Crash rate: <1%
- App size: <50MB

This comprehensive prompt provides all necessary requirements for developing a professional chess application ready for Google Play Store deployment. Ensure thorough testing and adherence to Google Play policies before submission.