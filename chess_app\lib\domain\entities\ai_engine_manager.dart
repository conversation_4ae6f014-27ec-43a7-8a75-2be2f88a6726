import 'dart:async';
import 'chess_ai_engine.dart';
import 'stockfish_engine.dart';
import 'ai_player.dart';
import 'chess_engine.dart';
import 'move.dart';

/// Enum for available AI engines
enum AIEngineType {
  basic,
  advanced,
  stockfish,
}

/// Manages different AI chess engines
class AIEngineManager {
  static final AIEngineManager _instance = AIEngineManager._internal();
  factory AIEngineManager() => _instance;
  AIEngineManager._internal();

  ChessAIEngine? _currentEngine;
  AIEngineType _currentEngineType = AIEngineType.advanced;
  bool _isInitialized = false;

  /// Gets the current engine type
  AIEngineType get currentEngineType => _currentEngineType;

  /// Gets the current engine
  ChessAIEngine? get currentEngine => _currentEngine;

  /// Initialize the AI engine manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _switchEngine(AIEngineType.advanced);
    _isInitialized = true;
  }

  /// Switch to a different AI engine
  Future<bool> switchEngine(AIEngineType engineType) async {
    if (_currentEngineType == engineType && _currentEngine != null) {
      return true;
    }

    return await _switchEngine(engineType);
  }

  Future<bool> _switchEngine(AIEngineType engineType) async {
    // Dispose current engine
    _currentEngine?.dispose();
    _currentEngine = null;

    try {
      switch (engineType) {
        case AIEngineType.basic:
          _currentEngine = BasicAIEngine();
          break;

        case AIEngineType.advanced:
          _currentEngine = AdvancedChessAI();
          break;

        case AIEngineType.stockfish:
          final stockfish = StockfishEngine();
          final initialized = await stockfish.initialize();
          if (initialized) {
            _currentEngine = stockfish;
          } else {
            // Fallback to advanced AI if Stockfish fails
            print('Stockfish initialization failed, falling back to Advanced AI');
            _currentEngine = AdvancedChessAI();
            engineType = AIEngineType.advanced;
          }
          break;
      }

      _currentEngineType = engineType;
      return _currentEngine != null;
    } catch (e) {
      print('Error switching to engine $engineType: $e');
      
      // Fallback to basic AI
      _currentEngine = BasicAIEngine();
      _currentEngineType = AIEngineType.basic;
      return false;
    }
  }

  /// Get the best move from the current engine
  Future<Move?> getBestMove(ChessEngine engine, {Duration? timeLimit}) async {
    if (_currentEngine == null) {
      await initialize();
    }

    return await _currentEngine?.getBestMove(engine, timeLimit: timeLimit);
  }

  /// Analyze position with the current engine
  Future<PositionAnalysis> analyzePosition(ChessEngine engine) async {
    if (_currentEngine == null) {
      await initialize();
    }

    return await _currentEngine?.analyzePosition(engine) ?? 
           PositionAnalysis(
             evaluation: 0,
             depth: 0,
             analysisTime: Duration.zero,
           );
  }

  /// Set the engine strength
  void setStrength(int strength) {
    _currentEngine?.setStrength(strength);
  }

  /// Get available engines
  List<AIEngineInfo> getAvailableEngines() {
    return [
      AIEngineInfo(
        type: AIEngineType.basic,
        name: 'Basic AI',
        description: 'Simple rule-based AI, good for beginners',
        strengthRange: '800-1400 ELO',
        isAvailable: true,
      ),
      AIEngineInfo(
        type: AIEngineType.advanced,
        name: 'Advanced AI',
        description: 'Enhanced AI with positional evaluation',
        strengthRange: '1000-2500 ELO',
        isAvailable: true,
      ),
      AIEngineInfo(
        type: AIEngineType.stockfish,
        name: 'Stockfish',
        description: 'World-class chess engine (requires installation)',
        strengthRange: '1200-3000+ ELO',
        isAvailable: _isStockfishAvailable(),
      ),
    ];
  }

  /// Check if Stockfish is available on the system
  bool _isStockfishAvailable() {
    // This is a simplified check - in a real implementation,
    // you'd try to execute stockfish and check the result
    return false; // For now, assume it's not available
  }

  /// Stop current analysis
  void stop() {
    _currentEngine?.stop();
  }

  /// Dispose all resources
  void dispose() {
    _currentEngine?.dispose();
    _currentEngine = null;
    _isInitialized = false;
  }
}

/// Information about an AI engine
class AIEngineInfo {
  final AIEngineType type;
  final String name;
  final String description;
  final String strengthRange;
  final bool isAvailable;

  const AIEngineInfo({
    required this.type,
    required this.name,
    required this.description,
    required this.strengthRange,
    required this.isAvailable,
  });
}

/// Basic AI engine (wrapper around the original AIPlayer)
class BasicAIEngine extends ChessAIEngine {
  late AIPlayer _aiPlayer;
  int _strength = 1200;

  BasicAIEngine() {
    _aiPlayer = AIPlayer(difficulty: AIDifficulty.medium);
  }

  @override
  String get name => 'Basic AI';

  @override
  int get strength => _strength;

  @override
  void setStrength(int strength) {
    _strength = strength.clamp(800, 1400);
    
    // Map strength to difficulty
    if (_strength < 900) {
      _aiPlayer = AIPlayer(difficulty: AIDifficulty.easy);
    } else if (_strength < 1100) {
      _aiPlayer = AIPlayer(difficulty: AIDifficulty.medium);
    } else {
      _aiPlayer = AIPlayer(difficulty: AIDifficulty.hard);
    }
  }

  @override
  Future<Move?> getBestMove(ChessEngine engine, {Duration? timeLimit}) async {
    return await _aiPlayer.getBestMove(engine);
  }

  @override
  Future<PositionAnalysis> analyzePosition(ChessEngine engine) async {
    final move = await _aiPlayer.getBestMove(engine);
    return PositionAnalysis(
      evaluation: 0, // Basic AI doesn't provide evaluation
      bestMove: move,
      depth: 1,
      analysisTime: const Duration(milliseconds: 500),
    );
  }

  @override
  void stop() {
    // Basic AI doesn't support stopping
  }

  @override
  void dispose() {
    // Nothing to dispose for basic AI
  }
}
