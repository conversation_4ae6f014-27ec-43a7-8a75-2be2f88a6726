import 'package:equatable/equatable.dart';
import 'board.dart';
import 'move.dart';
import 'piece.dart';
import 'position.dart';

/// Enum for game status
enum GameStatus {
  playing,
  check,
  checkmate,
  stalemate,
  draw,
}

/// Enum for game result
enum GameResult {
  whiteWins,
  blackWins,
  draw,
  ongoing,
}

/// Represents the complete state of a chess game
class GameState extends Equatable {
  final Board board;
  final PieceColor currentPlayer;
  final List<Move> moveHistory;
  final GameStatus status;
  final Position? enPassantTarget;
  final bool whiteCanCastleKingside;
  final bool whiteCanCastleQueenside;
  final bool blackCanCastleKingside;
  final bool blackCanCastleQueenside;
  final int halfMoveClock; // For 50-move rule
  final int fullMoveNumber;

  const GameState({
    required this.board,
    required this.currentPlayer,
    required this.moveHistory,
    required this.status,
    this.enPassantTarget,
    this.whiteCanCastleKingside = true,
    this.whiteCanCastleQueenside = true,
    this.blackCanCastleKingside = true,
    this.blackCanCastleQueenside = true,
    this.halfMoveClock = 0,
    this.fullMoveNumber = 1,
  });

  /// Creates the initial game state
  factory GameState.initial() {
    return GameState(
      board: Board.initial(),
      currentPlayer: PieceColor.white,
      moveHistory: [],
      status: GameStatus.playing,
    );
  }

  /// Creates a copy of the game state with updated properties
  GameState copyWith({
    Board? board,
    PieceColor? currentPlayer,
    List<Move>? moveHistory,
    GameStatus? status,
    Position? enPassantTarget,
    bool? whiteCanCastleKingside,
    bool? whiteCanCastleQueenside,
    bool? blackCanCastleKingside,
    bool? blackCanCastleQueenside,
    int? halfMoveClock,
    int? fullMoveNumber,
  }) {
    return GameState(
      board: board ?? this.board,
      currentPlayer: currentPlayer ?? this.currentPlayer,
      moveHistory: moveHistory ?? this.moveHistory,
      status: status ?? this.status,
      enPassantTarget: enPassantTarget,
      whiteCanCastleKingside: whiteCanCastleKingside ?? this.whiteCanCastleKingside,
      whiteCanCastleQueenside: whiteCanCastleQueenside ?? this.whiteCanCastleQueenside,
      blackCanCastleKingside: blackCanCastleKingside ?? this.blackCanCastleKingside,
      blackCanCastleQueenside: blackCanCastleQueenside ?? this.blackCanCastleQueenside,
      halfMoveClock: halfMoveClock ?? this.halfMoveClock,
      fullMoveNumber: fullMoveNumber ?? this.fullMoveNumber,
    );
  }

  /// Gets the game result based on current status
  GameResult get result {
    switch (status) {
      case GameStatus.checkmate:
        return currentPlayer == PieceColor.white 
            ? GameResult.blackWins 
            : GameResult.whiteWins;
      case GameStatus.stalemate:
      case GameStatus.draw:
        return GameResult.draw;
      default:
        return GameResult.ongoing;
    }
  }

  /// Checks if the game is over
  bool get isGameOver {
    return status == GameStatus.checkmate ||
           status == GameStatus.stalemate ||
           status == GameStatus.draw;
  }

  /// Gets the last move played
  Move? get lastMove {
    return moveHistory.isNotEmpty ? moveHistory.last : null;
  }

  /// Checks if a color can castle kingside
  bool canCastleKingside(PieceColor color) {
    return color == PieceColor.white 
        ? whiteCanCastleKingside 
        : blackCanCastleKingside;
  }

  /// Checks if a color can castle queenside
  bool canCastleQueenside(PieceColor color) {
    return color == PieceColor.white 
        ? whiteCanCastleQueenside 
        : blackCanCastleQueenside;
  }

  /// Updates castling rights after a move
  GameState updateCastlingRights(Move move) {
    bool newWhiteKingside = whiteCanCastleKingside;
    bool newWhiteQueenside = whiteCanCastleQueenside;
    bool newBlackKingside = blackCanCastleKingside;
    bool newBlackQueenside = blackCanCastleQueenside;

    // King moves disable all castling for that color
    if (move.piece is King) {
      if (move.piece.color == PieceColor.white) {
        newWhiteKingside = false;
        newWhiteQueenside = false;
      } else {
        newBlackKingside = false;
        newBlackQueenside = false;
      }
    }

    // Rook moves disable castling on that side
    if (move.piece is Rook) {
      if (move.piece.color == PieceColor.white) {
        if (move.from == const Position(row: 7, col: 0)) {
          newWhiteQueenside = false;
        } else if (move.from == const Position(row: 7, col: 7)) {
          newWhiteKingside = false;
        }
      } else {
        if (move.from == const Position(row: 0, col: 0)) {
          newBlackQueenside = false;
        } else if (move.from == const Position(row: 0, col: 7)) {
          newBlackKingside = false;
        }
      }
    }

    return copyWith(
      whiteCanCastleKingside: newWhiteKingside,
      whiteCanCastleQueenside: newWhiteQueenside,
      blackCanCastleKingside: newBlackKingside,
      blackCanCastleQueenside: newBlackQueenside,
    );
  }

  @override
  List<Object?> get props => [
        board,
        currentPlayer,
        moveHistory,
        status,
        enPassantTarget,
        whiteCanCastleKingside,
        whiteCanCastleQueenside,
        blackCanCastleKingside,
        blackCanCastleQueenside,
        halfMoveClock,
        fullMoveNumber,
      ];
}
