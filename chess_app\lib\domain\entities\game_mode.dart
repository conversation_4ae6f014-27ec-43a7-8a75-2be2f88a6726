import 'package:equatable/equatable.dart';
import 'piece.dart';
import 'ai_player.dart';

/// Enum for different game modes
enum GameModeType {
  humanVsHuman,
  humanVsAI,
  aiVsAI,
  puzzle,
  analysis,
}

/// Enum for time control types
enum TimeControlType {
  none,
  blitz,
  rapid,
  classical,
  custom,
}

/// Represents time control settings
class TimeControl extends Equatable {
  final TimeControlType type;
  final Duration initialTime;
  final Duration increment;
  final String name;

  const TimeControl({
    required this.type,
    required this.initialTime,
    required this.increment,
    required this.name,
  });

  /// Predefined time controls
  static const TimeControl noTime = TimeControl(
    type: TimeControlType.none,
    initialTime: Duration.zero,
    increment: Duration.zero,
    name: 'No Time Limit',
  );

  static const TimeControl blitz3 = TimeControl(
    type: TimeControlType.blitz,
    initialTime: Duration(minutes: 3),
    increment: Duration.zero,
    name: '3 min',
  );

  static const TimeControl blitz5 = TimeControl(
    type: TimeControlType.blitz,
    initialTime: Duration(minutes: 5),
    increment: Duration.zero,
    name: '5 min',
  );

  static const TimeControl rapid10 = TimeControl(
    type: TimeControlType.rapid,
    initialTime: Duration(minutes: 10),
    increment: Duration.zero,
    name: '10 min',
  );

  static const TimeControl rapid15 = TimeControl(
    type: TimeControlType.rapid,
    initialTime: Duration(minutes: 15),
    increment: Duration(seconds: 10),
    name: '15+10',
  );

  static const TimeControl classical30 = TimeControl(
    type: TimeControlType.classical,
    initialTime: Duration(minutes: 30),
    increment: Duration.zero,
    name: '30 min',
  );

  @override
  List<Object> get props => [type, initialTime, increment, name];
}

/// Represents a game mode configuration
class GameMode extends Equatable {
  final GameModeType type;
  final String name;
  final String description;
  final PieceColor playerColor;
  final AIDifficulty? aiDifficulty;
  final TimeControl timeControl;
  final bool soundEnabled;
  final bool hintsEnabled;
  final bool analysisEnabled;

  const GameMode({
    required this.type,
    required this.name,
    required this.description,
    this.playerColor = PieceColor.white,
    this.aiDifficulty,
    this.timeControl = TimeControl.noTime,
    this.soundEnabled = true,
    this.hintsEnabled = false,
    this.analysisEnabled = false,
  });

  /// Creates a copy with updated properties
  GameMode copyWith({
    GameModeType? type,
    String? name,
    String? description,
    PieceColor? playerColor,
    AIDifficulty? aiDifficulty,
    TimeControl? timeControl,
    bool? soundEnabled,
    bool? hintsEnabled,
    bool? analysisEnabled,
  }) {
    return GameMode(
      type: type ?? this.type,
      name: name ?? this.name,
      description: description ?? this.description,
      playerColor: playerColor ?? this.playerColor,
      aiDifficulty: aiDifficulty ?? this.aiDifficulty,
      timeControl: timeControl ?? this.timeControl,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      hintsEnabled: hintsEnabled ?? this.hintsEnabled,
      analysisEnabled: analysisEnabled ?? this.analysisEnabled,
    );
  }

  /// Predefined game modes
  static const GameMode humanVsHuman = GameMode(
    type: GameModeType.humanVsHuman,
    name: 'Human vs Human',
    description: 'Play against another human player on the same device',
  );

  static const GameMode humanVsAIEasy = GameMode(
    type: GameModeType.humanVsAI,
    name: 'vs Computer (Easy)',
    description: 'Play against an easy AI opponent',
    aiDifficulty: AIDifficulty.easy,
    hintsEnabled: true,
  );

  static const GameMode humanVsAIMedium = GameMode(
    type: GameModeType.humanVsAI,
    name: 'vs Computer (Medium)',
    description: 'Play against a medium AI opponent',
    aiDifficulty: AIDifficulty.medium,
  );

  static const GameMode humanVsAIHard = GameMode(
    type: GameModeType.humanVsAI,
    name: 'vs Computer (Hard)',
    description: 'Play against a hard AI opponent',
    aiDifficulty: AIDifficulty.hard,
  );

  static const GameMode humanVsAIExpert = GameMode(
    type: GameModeType.humanVsAI,
    name: 'vs Computer (Expert)',
    description: 'Play against an expert AI opponent',
    aiDifficulty: AIDifficulty.expert,
  );

  static const GameMode puzzleMode = GameMode(
    type: GameModeType.puzzle,
    name: 'Puzzle Mode',
    description: 'Solve chess puzzles and improve your tactics',
    hintsEnabled: true,
    analysisEnabled: true,
  );

  static const GameMode analysisMode = GameMode(
    type: GameModeType.analysis,
    name: 'Analysis Mode',
    description: 'Analyze positions and games',
    analysisEnabled: true,
  );

  /// Gets all available game modes
  static List<GameMode> getAllModes() {
    return [
      humanVsHuman,
      humanVsAIEasy,
      humanVsAIMedium,
      humanVsAIHard,
      humanVsAIExpert,
      puzzleMode,
      analysisMode,
    ];
  }

  /// Gets AI game modes only
  static List<GameMode> getAIModes() {
    return [
      humanVsAIEasy,
      humanVsAIMedium,
      humanVsAIHard,
      humanVsAIExpert,
    ];
  }

  /// Gets available time controls
  static List<TimeControl> getTimeControls() {
    return [
      TimeControl.noTime,
      TimeControl.blitz3,
      TimeControl.blitz5,
      TimeControl.rapid10,
      TimeControl.rapid15,
      TimeControl.classical30,
    ];
  }

  @override
  List<Object?> get props => [
        type,
        name,
        description,
        playerColor,
        aiDifficulty,
        timeControl,
        soundEnabled,
        hintsEnabled,
        analysisEnabled,
      ];
}
