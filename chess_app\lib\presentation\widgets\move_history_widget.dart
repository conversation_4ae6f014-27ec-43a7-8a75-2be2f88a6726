import 'package:flutter/material.dart';
import '../../domain/entities/move.dart';
import '../../domain/entities/piece.dart';

/// Widget that displays the move history
class MoveHistoryWidget extends StatelessWidget {
  final List<Move> moves;
  final Function(int)? onMoveSelected;
  final int? selectedMoveIndex;

  const MoveHistoryWidget({
    Key? key,
    required this.moves,
    this.onMoveSelected,
    this.selectedMoveIndex,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (moves.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'No moves yet',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Move History',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: (moves.length + 1) ~/ 2, // Number of move pairs
              itemBuilder: (context, index) {
                final whiteMove = moves[index * 2];
                final blackMove = index * 2 + 1 < moves.length ? moves[index * 2 + 1] : null;
                
                return _buildMoveRow(context, index + 1, whiteMove, blackMove);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoveRow(BuildContext context, int moveNumber, Move whiteMove, Move? blackMove) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          // Move number
          SizedBox(
            width: 30,
            child: Text(
              '$moveNumber.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ),
          
          // White move
          Expanded(
            child: _buildMoveButton(
              context,
              whiteMove,
              (moveNumber - 1) * 2,
              PieceColor.white,
            ),
          ),
          
          // Black move
          Expanded(
            child: blackMove != null
                ? _buildMoveButton(
                    context,
                    blackMove,
                    (moveNumber - 1) * 2 + 1,
                    PieceColor.black,
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildMoveButton(BuildContext context, Move move, int index, PieceColor color) {
    final isSelected = selectedMoveIndex == index;
    
    return GestureDetector(
      onTap: onMoveSelected != null ? () => onMoveSelected!(index) : null,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).primaryColor.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          border: isSelected 
              ? Border.all(color: Theme.of(context).primaryColor)
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Piece icon
            Text(
              move.piece.symbol,
              style: TextStyle(
                fontSize: 16,
                color: color == PieceColor.white ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(width: 4),
            
            // Move notation
            Expanded(
              child: Text(
                move.toAlgebraicNotation(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Theme.of(context).primaryColor : null,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Special move indicators
            if (move.isCheck) 
              Icon(
                Icons.warning,
                size: 12,
                color: Colors.orange,
              ),
            if (move.isCheckmate)
              Icon(
                Icons.flag,
                size: 12,
                color: Colors.red,
              ),
            if (move.isCastling)
              Icon(
                Icons.castle,
                size: 12,
                color: Colors.blue,
              ),
          ],
        ),
      ),
    );
  }
}
