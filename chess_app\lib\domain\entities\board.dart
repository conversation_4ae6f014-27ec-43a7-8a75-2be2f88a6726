import 'package:equatable/equatable.dart';
import 'position.dart';
import 'piece.dart';

/// Represents the chess board state
class Board extends Equatable {
  final List<List<ChessPiece?>> squares;

  const Board(this.squares);

  /// Creates an empty board
  factory Board.empty() {
    return Board(List.generate(8, (_) => List.filled(8, null)));
  }

  /// Creates a board with the standard starting position
  factory Board.initial() {
    final board = Board.empty();
    
    // Place white pieces
    board._setPiece(7, 0, const Rook(color: PieceColor.white));
    board._setPiece(7, 1, const Knight(color: PieceColor.white));
    board._setPiece(7, 2, const Bishop(color: PieceColor.white));
    board._setPiece(7, 3, const Queen(color: PieceColor.white));
    board._setPiece(7, 4, const King(color: PieceColor.white));
    board._setPiece(7, 5, const Bishop(color: PieceColor.white));
    board._set<PERSON>iece(7, 6, const Knight(color: PieceColor.white));
    board._setPiece(7, 7, const Rook(color: PieceColor.white));
    
    for (int col = 0; col < 8; col++) {
      board._setPiece(6, col, const Pawn(color: PieceColor.white));
    }
    
    // Place black pieces
    board._setPiece(0, 0, const Rook(color: PieceColor.black));
    board._setPiece(0, 1, const Knight(color: PieceColor.black));
    board._setPiece(0, 2, const Bishop(color: PieceColor.black));
    board._setPiece(0, 3, const Queen(color: PieceColor.black));
    board._setPiece(0, 4, const King(color: PieceColor.black));
    board._setPiece(0, 5, const Bishop(color: PieceColor.black));
    board._setPiece(0, 6, const Knight(color: PieceColor.black));
    board._setPiece(0, 7, const Rook(color: PieceColor.black));
    
    for (int col = 0; col < 8; col++) {
      board._setPiece(1, col, const Pawn(color: PieceColor.black));
    }
    
    return board;
  }

  /// Gets the piece at the given position
  ChessPiece? getPiece(Position position) {
    if (!position.isValid) return null;
    return squares[position.row][position.col];
  }

  /// Gets the piece at the given row and column
  ChessPiece? getPieceAt(int row, int col) {
    if (row < 0 || row >= 8 || col < 0 || col >= 8) return null;
    return squares[row][col];
  }

  /// Sets a piece at the given position (private method for initialization)
  void _setPiece(int row, int col, ChessPiece? piece) {
    squares[row][col] = piece;
  }

  /// Creates a new board with a piece moved from one position to another
  Board makeMove(Position from, Position to, {ChessPiece? promotionPiece}) {
    final newSquares = squares.map((row) => List<ChessPiece?>.from(row)).toList();
    final piece = newSquares[from.row][from.col];
    
    if (piece == null) return Board(newSquares);
    
    // Handle promotion
    ChessPiece pieceToPlace = piece;
    if (promotionPiece != null) {
      pieceToPlace = promotionPiece;
    } else {
      // Mark piece as moved
      pieceToPlace = piece.copyWith(hasMoved: true);
    }
    
    // Move the piece
    newSquares[from.row][from.col] = null;
    newSquares[to.row][to.col] = pieceToPlace;
    
    return Board(newSquares);
  }

  /// Checks if a square is empty
  bool isSquareEmpty(Position position) {
    return getPiece(position) == null;
  }

  /// Checks if a square is occupied by a piece of the given color
  bool isSquareOccupiedBy(Position position, PieceColor color) {
    final piece = getPiece(position);
    return piece != null && piece.color == color;
  }

  /// Finds the king of the given color
  Position? findKing(PieceColor color) {
    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = squares[row][col];
        if (piece is King && piece.color == color) {
          return Position(row: row, col: col);
        }
      }
    }
    return null;
  }

  /// Gets all pieces of the given color
  List<MapEntry<Position, ChessPiece>> getPiecesOfColor(PieceColor color) {
    final pieces = <MapEntry<Position, ChessPiece>>[];
    
    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = squares[row][col];
        if (piece != null && piece.color == color) {
          pieces.add(MapEntry(Position(row: row, col: col), piece));
        }
      }
    }
    
    return pieces;
  }

  /// Creates a copy of the board
  Board copy() {
    final newSquares = squares.map((row) => List<ChessPiece?>.from(row)).toList();
    return Board(newSquares);
  }

  @override
  List<Object?> get props => [squares];

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('  a b c d e f g h');
    
    for (int row = 0; row < 8; row++) {
      buffer.write('${8 - row} ');
      for (int col = 0; col < 8; col++) {
        final piece = squares[row][col];
        buffer.write(piece?.symbol ?? '.');
        buffer.write(' ');
      }
      buffer.writeln('${8 - row}');
    }
    
    buffer.writeln('  a b c d e f g h');
    return buffer.toString();
  }
}
