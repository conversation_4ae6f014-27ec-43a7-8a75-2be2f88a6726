import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/game_mode.dart';
import '../../domain/entities/piece.dart';
import '../bloc/game_bloc.dart';
import '../bloc/game_event.dart';

/// Home screen with game mode selection
class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chess Master'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.pushNamed(context, '/settings'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Welcome section
            _buildWelcomeSection(context),
            
            const SizedBox(height: 30),
            
            // Quick play section
            _buildQuickPlaySection(context),
            
            const SizedBox(height: 30),
            
            // Game modes section
            _buildGameModesSection(context),
            
            const SizedBox(height: 30),
            
            // Additional features
            _buildAdditionalFeatures(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.sports_esports,
              size: 60,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Welcome to Chess Master',
              style: Theme.of(context).textTheme.headlineMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Choose your game mode and start playing!',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickPlaySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Play',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _startGame(context, GameMode.humanVsHuman),
                icon: const Icon(Icons.people),
                label: const Text('vs Human'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _startGame(context, GameMode.humanVsAIMedium),
                icon: const Icon(Icons.computer),
                label: const Text('vs Computer'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGameModesSection(BuildContext context) {
    final gameModes = GameMode.getAllModes();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Game Modes',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        ...gameModes.map((mode) => _buildGameModeCard(context, mode)),
      ],
    );
  }

  Widget _buildGameModeCard(BuildContext context, GameMode mode) {
    IconData icon;
    switch (mode.type) {
      case GameModeType.humanVsHuman:
        icon = Icons.people;
        break;
      case GameModeType.humanVsAI:
        icon = Icons.computer;
        break;
      case GameModeType.puzzle:
        icon = Icons.extension;
        break;
      case GameModeType.analysis:
        icon = Icons.analytics;
        break;
      default:
        icon = Icons.games;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
        title: Text(mode.name),
        subtitle: Text(mode.description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _startGame(context, mode),
      ),
    );
  }

  Widget _buildAdditionalFeatures(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Features',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Card(
          child: Column(
            children: [
              ListTile(
                leading: Icon(
                  Icons.history,
                  color: Theme.of(context).colorScheme.primary,
                ),
                title: const Text('Game History'),
                subtitle: const Text('View your previous games'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // TODO: Navigate to game history
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Game history coming soon!')),
                  );
                },
              ),
              const Divider(height: 1),
              ListTile(
                leading: Icon(
                  Icons.school,
                  color: Theme.of(context).colorScheme.primary,
                ),
                title: const Text('Learn Chess'),
                subtitle: const Text('Tutorials and tips'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // TODO: Navigate to tutorials
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Tutorials coming soon!')),
                  );
                },
              ),
              const Divider(height: 1),
              ListTile(
                leading: Icon(
                  Icons.leaderboard,
                  color: Theme.of(context).colorScheme.primary,
                ),
                title: const Text('Leaderboard'),
                subtitle: const Text('See top players'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // TODO: Navigate to leaderboard
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Leaderboard coming soon!')),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _startGame(BuildContext context, GameMode mode) {
    final gameBloc = context.read<GameBloc>();
    
    gameBloc.add(StartNewGameEvent(
      playerColor: mode.playerColor,
      isAIGame: mode.type == GameModeType.humanVsAI,
    ));
    
    Navigator.pushNamed(context, '/game');
  }
}
