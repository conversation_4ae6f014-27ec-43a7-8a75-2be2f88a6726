import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/game_bloc.dart';
import '../bloc/game_event.dart';
import '../bloc/game_state_bloc.dart';
import '../widgets/chess_board_widget.dart';
import '../widgets/game_info_widget.dart';
import '../widgets/captured_pieces_widget.dart';
import '../widgets/move_history_widget.dart';
import '../../domain/entities/piece.dart';
import '../../domain/entities/game_state.dart';

/// Main game screen where chess is played
class GameScreen extends StatelessWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chess Game'),
        actions: [
          IconButton(
            icon: const Icon(Icons.pause),
            onPressed: () =>
                context.read<GameBloc>().add(const PauseGameEvent()),
          ),
          IconButton(
            icon: const Icon(Icons.undo),
            onPressed: () =>
                context.read<GameBloc>().add(const UndoMoveEvent()),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new_game',
                child: ListTile(
                  leading: Icon(Icons.refresh),
                  title: Text('New Game'),
                ),
              ),
              const PopupMenuItem(
                value: 'resign',
                child: ListTile(
                  leading: Icon(Icons.flag),
                  title: Text('Resign'),
                ),
              ),
              const PopupMenuItem(
                value: 'draw',
                child: ListTile(
                  leading: Icon(Icons.handshake),
                  title: Text('Offer Draw'),
                ),
              ),
              const PopupMenuItem(
                value: 'flip',
                child: ListTile(
                  leading: Icon(Icons.flip),
                  title: Text('Flip Board'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<GameBloc, GameBlocState>(
        listener: (context, state) {
          if (state is GameFinished) {
            _showGameEndDialog(context, state);
          } else if (state is GameError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is DrawOffered) {
            _showDrawOfferDialog(context, state);
          }
        },
        builder: (context, state) {
          if (state is GameLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is GameInProgress) {
            return _buildGameInProgress(context, state);
          }

          if (state is AIThinking) {
            return _buildAIThinking(context, state);
          }

          if (state is GamePaused) {
            return _buildGamePaused(context, state);
          }

          return const Center(
            child: Text('Game not started'),
          );
        },
      ),
    );
  }

  Widget _buildGameInProgress(BuildContext context, GameInProgress state) {
    return Column(
      children: [
        // Top captured pieces (opponent)
        CapturedPiecesWidget(
          capturedPieces:
              _getCapturedPieces(state, _getOpponentColor(state.playerColor)),
          color: _getOpponentColor(state.playerColor),
          isTop: true,
        ),

        // Game info
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GameInfoWidget(
            gameState: state.gameState,
            whiteTime: state.whiteTime,
            blackTime: state.blackTime,
          ),
        ),

        // Chess board
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ChessBoardWidget(
              board: state.gameState.board,
              onMovePiece: (move) =>
                  context.read<GameBloc>().add(MakeMoveEvent(move)),
              highlightedSquares: state.highlightedSquares,
              selectedSquare: state.selectedSquare,
              validMoves: state.validMoves,
              isPlayerTurn: state.isPlayerTurn,
              playerColor: state.playerColor,
              currentPlayer: state.gameState.currentPlayer,
            ),
          ),
        ),

        // Bottom captured pieces (player)
        CapturedPiecesWidget(
          capturedPieces: _getCapturedPieces(state, state.playerColor),
          color: state.playerColor,
          isTop: false,
        ),

        // Move history
        SizedBox(
          height: 120,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: MoveHistoryWidget(
              moves: state.gameState.moveHistory,
              onMoveSelected: (index) =>
                  context.read<GameBloc>().add(NavigateToMoveEvent(index)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAIThinking(BuildContext context, AIThinking state) {
    return Column(
      children: [
        Expanded(
          child: ChessBoardWidget(
            board: state.gameState.board,
            onMovePiece: (move) =>
                context.read<GameBloc>().add(MakeMoveEvent(move)),
            highlightedSquares: const [],
            isPlayerTurn: false,
            playerColor: state.playerColor,
            currentPlayer: state.gameState.currentPlayer,
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              const SizedBox(width: 16),
              Text(
                'AI is thinking...',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGamePaused(BuildContext context, GamePaused state) {
    return Center(
      child: Card(
        margin: const EdgeInsets.all(32),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.pause_circle_outline,
                size: 64,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Game Paused',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () =>
                    context.read<GameBloc>().add(const ResumeGameEvent()),
                child: const Text('Resume Game'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    final gameBloc = context.read<GameBloc>();

    switch (action) {
      case 'new_game':
        _showNewGameDialog(context);
        break;
      case 'resign':
        _showResignDialog(context);
        break;
      case 'draw':
        gameBloc.add(const OfferDrawEvent());
        break;
      case 'flip':
        gameBloc.add(const FlipBoardEvent());
        break;
    }
  }

  void _showGameEndDialog(BuildContext context, GameFinished state) {
    String title;
    String message;

    switch (state.result) {
      case GameResult.whiteWins:
        title = 'White Wins!';
        message = 'Congratulations to White!';
        break;
      case GameResult.blackWins:
        title = 'Black Wins!';
        message = 'Congratulations to Black!';
        break;
      case GameResult.draw:
        title = 'Draw!';
        message = 'The game ended in a draw.';
        break;
      default:
        title = 'Game Over';
        message = 'The game has ended.';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message),
            const SizedBox(height: 8),
            Text('Reason: ${state.reason}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushReplacementNamed('/home');
            },
            child: const Text('Home'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameBloc>().add(const StartNewGameEvent());
            },
            child: const Text('New Game'),
          ),
        ],
      ),
    );
  }

  void _showDrawOfferDialog(BuildContext context, DrawOffered state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Draw Offer'),
        content:
            Text('${state.offeringPlayer.name} offers a draw. Do you accept?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameBloc>().add(const DeclineDrawEvent());
            },
            child: const Text('Decline'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameBloc>().add(const AcceptDrawEvent());
            },
            child: const Text('Accept'),
          ),
        ],
      ),
    );
  }

  void _showNewGameDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('New Game'),
        content: const Text(
            'Are you sure you want to start a new game? Current progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameBloc>().add(const StartNewGameEvent());
            },
            child: const Text('New Game'),
          ),
        ],
      ),
    );
  }

  void _showResignDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Resign'),
        content: const Text('Are you sure you want to resign?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Get current player color
              context
                  .read<GameBloc>()
                  .add(const ResignGameEvent(PieceColor.white));
            },
            child: const Text('Resign'),
          ),
        ],
      ),
    );
  }

  List<ChessPiece> _getCapturedPieces(GameInProgress state, PieceColor color) {
    // TODO: Implement captured pieces tracking
    return [];
  }

  PieceColor _getOpponentColor(PieceColor playerColor) {
    return playerColor == PieceColor.white
        ? PieceColor.black
        : PieceColor.white;
  }
}
